from fastapi import <PERSON><PERSON><PERSON>
from fastapi_pagination import add_pagination
from starlette.middleware.cors import CORSMiddleware

from app.api.v1.api import api_router as api_router_v1
from app.exceptions.handlers import register_exception_handlers
from app.services.db.middleware import SQLAlchemyMiddleware
from app.services.db.session import get_engine_args
from app.startup import lifespan
from config.loader import SettingsFactory

settings = SettingsFactory.settings()


app = FastAPI(lifespan=lifespan, redirect_slashes=False)

register_exception_handlers(app)
add_pagination(app)
app.add_middleware(
    SQLAlchemyMiddleware,
    db_url=str(settings.DATABASE_URI),
    engine_args=get_engine_args(settings),
)


if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


@app.get("/health/web")
async def read_root() -> dict:
    return {"status": "ok", "message": "Welcome to Hippocampus Cloud!"}


app.include_router(api_router_v1, prefix=settings.API_V1_STR)
