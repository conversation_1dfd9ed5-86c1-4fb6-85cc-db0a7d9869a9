# app/runtime/__init__.py
from logging import Logger
from .handles import CONFIG_HANDLE, LOGGER_HANDLE, RELOAD_HANDLE
from .proxies import LazyProxy
from app.services.config.settings import AppSettings
from app.services.reload.registry import ReloadRegistry

# llm      = LazyProxy(lambda: LLM_HANDLE.current(), "LLM")
# aws      = LazyProxy(lambda: AWS_HANDLE.current(), "AWS")
# firebase = LazyProxy(lambda: FIREBASE_HANDLE.current(), "FIREBASE")
# stripe   = LazyProxy(lambda: STRIPE_HANDLE.current(), "STRIPE")
# celery   = LazyProxy(lambda: CELERY_HANDLE.current(), "CELERY")
config: AppSettings = LazyProxy(lambda: CONFIG_HANDLE.current(), "CONFIG")
logger: Logger = LazyProxy(lambda: LOGGER_HANDLE.current(), "LOGGER")
# db       = ContextVarProxy(DB_SESSION_VAR, "DB")
reloader: ReloadRegistry = LazyProxy(lambda: RELOAD_HANDLE.current(), "RELOAD_REGISTRY")
