from typing import Any, Generic, TypeVar

T = TypeVar("T")


class AtomicRef(Generic[T]):
    __slots__ = ("_ref",)

    def __init__(self, initial: T):
        self._ref = initial

    def current(self) -> T:
        return self._ref

    def set(self, new: T):
        self._ref = new

    def swap(self, new: T):
        old, self._ref = self._ref, new
        return old


class _Uninit:
    def __init__(self, name):
        self.name = name

    def __getattr__(self, _):
        raise RuntimeError(f"{self.name} not initialized")


CONFIG_HANDLE = AtomicRef[_Uninit | object](_Uninit("CONFIG"))
LOGGER_HANDLE = AtomicRef[_Uninit | object](_Uninit("LOGGER"))
RELOAD_HANDLE = AtomicRef[_Uninit | Any](_Uninit("RELOAD_REGISTRY"))
