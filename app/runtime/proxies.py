class LazyProxy:
    __slots__ = ("getter", "name")

    def __init__(self, getter, name):
        self.getter = getter
        self.name = name

    def __getattr__(self, k):
        return getattr(self.getter(), k)


class ContextVarProxy:
    __slots__ = ("var", "name")

    def __init__(self, var, name):
        self.var = var
        self.name = name

    def __getattr__(self, k):
        try:
            obj = self.var.get()
        except LookupError:
            raise RuntimeError(f"{self.name} not set")
        return getattr(obj, k)
