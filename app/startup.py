import logging

from fastapi import <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import insert

from app.modules import cruds
from app.modules.permission import Permission
from app.modules.shared.base_policy import Action
from app.runtime import logger
from app.runtime.handles import CONFIG_HANDLE, LOGGER_HANDLE, RELOAD_HANDLE
from app.services.billing.sync import stripe_sync_service
from app.services.config.settings import STARTUP
from app.services.container import ServiceContainer
from app.services.db.session import SessionFactory


def get_default_permissions():
    """Define default permissions for each role.

    Returns a list of permission configurations that map roles to their allowed actions.
    """
    permissions_config = [
        # Platform super admin - full access
        (cruds.role.platform_super_admin, "*", Action.ALL),
        # Organization roles
        (cruds.role.org_owner, "*", Action.ALL),
        (cruds.role.org_admin, "*", Action.CREATE),
        (cruds.role.org_admin, "*", Action.READ),
        (cruds.role.org_admin, "*", Action.UPDATE),
        (cruds.role.org_viewer, "*", Action.READ),
        # Project roles
        (cruds.role.proj_owner, "*", Action.ALL),
        (cruds.role.proj_admin, "*", Action.CREATE),
        (cruds.role.proj_admin, "*", Action.READ),
        (cruds.role.proj_admin, "*", Action.UPDATE),
        (cruds.role.proj_viewer, "*", Action.READ),
    ]

    return permissions_config


async def setup_default_permissions(session):
    """Set up default permissions for all roles."""
    permission_data = []
    permissions_config = get_default_permissions()

    # Build permission data
    for role, resource, action in permissions_config:
        permission_data.append({"role_id": role.id, "resource": resource, "action": action, "scope": "*"})

    stmt = (
        insert(Permission)
        .values(permission_data)
        .on_conflict_do_nothing(index_elements=["role_id", "resource", "action", "scope"])
    )
    await session.execute(stmt)
    await session.commit()
    logger.info(f"[Startup] Initialized {len(permission_data)} default permissions")


async def lifespan(app: FastAPI):
    """Application startup tasks."""
    container = ServiceContainer()
    store = container.config_store()
    await store.refresh_for(STARTUP, submit=False)
    await container.init_resources()
    settings = container.settings()
    CONFIG_HANDLE.set(settings)
    app_logger = logging.getLogger("app")
    LOGGER_HANDLE.set(app_logger)
    reloader = await container.reloader()
    RELOAD_HANDLE.set(reloader)

    app.title = settings.project_name
    app.version = settings.api_version

    # Register organization resources
    async with SessionFactory.make_local_session() as session:
        # Build role cache first
        await cruds.role.build_cache(session=session)

        # Build subscription plan cache
        await cruds.subscription_plan.build_cache(session=session)

        # Set up default permissions
        await setup_default_permissions(session)
    if settings.stripe.sync_on_startup:
        try:
            await stripe_sync_service.sync_on_startup()
        except Exception as e:
            app_logger.error(f"Failed to perform initial Stripe sync: {e}")
            # Don't prevent startup, just log the error
    # Additional startup tasks can be added here
    app_logger.info("[Startup] Application initialized and resources registered.")
    yield
    await container.shutdown_resources()
