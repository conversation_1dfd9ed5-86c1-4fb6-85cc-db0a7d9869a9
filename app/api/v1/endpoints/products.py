from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.modules.shared.base_schema import IGetResponseBase, create_response
from app.modules.stripe_product.models import StripeProduct, StripeProductPrice
from app.modules.stripe_product.schema import (
    StripeProductPriceBase,
    StripeProductWithPricesResponse,
)
from app.services.billing import stripe_service
from app.services.db.session import new_session

router = APIRouter()


@router.get("/", response_model=IGetResponseBase[dict])
async def list_products(session: AsyncSession = Depends(new_session)):  # noqa: B008
    """List all products with their prices."""
    # Get all products
    products = await stripe_service.get_all_products()

    result = []
    for product in products:
        # Get prices for this product
        prices = await stripe_service.get_product_price(product.id)

        # Convert to response schema
        product_response = StripeProductWithPricesResponse(
            id=product.id,
            stripe_product_id=product.id,
            name=product.name,
            description=product.description,
            stripe_metadata=product.metadata,
            active=product.active,
            prices=[
                StripeProductPriceBase(
                    stripe_price_id=price.id,
                    product_id=price.product,
                    nickname=price.nickname,
                    amount=price.unit_amount,
                    currency=price.currency,
                    active=price.active,
                )
                for price in prices
            ],
        )
        result.append(product_response)
    product_items = {"items": result}

    return create_response(data=product_items, message="Products retrieved successfully")