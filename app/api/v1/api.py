from fastapi import APIRouter
from fastapi.routing import APIRoute

from app.api.v1.endpoints import (
    identity_resource,
    login,
    memories,
    products,
    service_incidents,
    stripe_checkout_sessions,
    stripe_sync,
    stripe_webhooks,
)
from app.modules.conversation import router as conversation
from app.modules.identity import router as identity
from app.modules.memory.router import router as memory_search_router
from app.modules.memory.task.router import router as memory_tasks
from app.modules.organization import router as organizations
from app.modules.organization.member import router as organization_members
from app.modules.organization.project import router as projects
from app.modules.organization.project.app.api_key import router as project_api_keys
from app.modules.organization.project.member import router as project_members
from app.modules.organization.project.memory_category import router as project_memory_categories
from app.modules.organization.subscription import router as organization_subscriptions

api_router = APIRouter()
api_router.include_router(login.router, prefix="/login", tags=["login"], include_in_schema=False)
api_router.include_router(identity_resource.router, include_in_schema=False)
api_router.include_router(identity.router, include_in_schema=False)
api_router.include_router(organizations.router, include_in_schema=False)
api_router.include_router(organization_members.router, include_in_schema=False)
api_router.include_router(organization_subscriptions.router, include_in_schema=False)
api_router.include_router(projects.router, include_in_schema=False)
api_router.include_router(project_members.router, include_in_schema=False)
api_router.include_router(project_memory_categories.router, include_in_schema=False)
api_router.include_router(project_api_keys.router, include_in_schema=False)
# api_router.include_router(invitations.router, prefix="/invitations", tags=["invitations"])
api_router.include_router(memories.router, prefix="/memory", tags=["memory"])
api_router.include_router(memory_tasks, include_in_schema=False)
api_router.include_router(conversation.router, include_in_schema=False)
api_router.include_router(memory_search_router, tags=["memory-search"], include_in_schema=False)

api_router.include_router(stripe_sync.router, prefix="/manual_sync", tags=["billing"], include_in_schema=False)
api_router.include_router(products.router, prefix="/products", tags=["products"], include_in_schema=False)
api_router.include_router(
    stripe_checkout_sessions.router, prefix="/stripe-checkout", tags=["checkout"], include_in_schema=False
)
api_router.include_router(
    service_incidents.router, prefix="/service-incidents", tags=["service-incidents"], include_in_schema=False
)
api_router.include_router(stripe_webhooks.router, prefix="/webhooks", tags=["webhooks"], include_in_schema=False)


def remove_trailing_slashes_from_routes(parent: APIRouter) -> APIRouter:
    "Removes trailing slashes from all routes in the given router"

    for route in parent.routes:
        if isinstance(route, APIRoute):
            route.path = route.path.rstrip("/")

    return parent


remove_trailing_slashes_from_routes(api_router)
