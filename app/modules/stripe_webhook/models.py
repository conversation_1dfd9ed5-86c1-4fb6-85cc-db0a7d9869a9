from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING

from sqlalchemy import BigInteger, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, Relationship

from app.modules.shared.base_model import BaseModelMixin

if TYPE_CHECKING:
    from app.modules.identity import Identity
    from app.modules.organization import Organization


class WebhookEventStatus(str, Enum):
    """Status of webhook event processing."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    DUPLICATE = "duplicate"


class StripeWebhookEvent(BaseModelMixin, table=True):
    """Model to store Stripe webhook events for audit and idempotency."""
    __tablename__ = "stripe_webhook_events"

    # Stripe event details
    stripe_event_id: str = Field(unique=True, nullable=False, max_length=255, index=True)
    event_type: str = Field(nullable=False, max_length=100, index=True)
    stripe_created: datetime = Field(nullable=False, description="Event creation time from Stripe")
    livemode: bool = Field(default=False, description="Whether this is a live or test event")
    
    # Processing details
    status: WebhookEventStatus = Field(default=WebhookEventStatus.PENDING, index=True)
    processed_at: datetime | None = Field(default=None, description="When the event was successfully processed")
    attempts: int = Field(default=0, description="Number of processing attempts")
    last_attempt_at: datetime | None = Field(default=None, description="Last processing attempt time")
    
    # Event data and metadata
    event_data: dict = Field(default_factory=dict, sa_type=JSONB, description="Full Stripe event data")
    processing_result: dict | None = Field(default=None, sa_type=JSONB, description="Processing result details")
    error_message: str | None = Field(default=None, sa_type=Text, description="Error message if processing failed")

    # Foreign keys
    identity_id: str | None = Field(foreign_key="identities.id", nullable=True, max_length=255, index=True)
    organization_id: str | None = Field(foreign_key="organizations.id", nullable=True, max_length=255, index=True)

    # Relationships
    identity: "Identity" = Relationship()
    organization: "Organization" = Relationship()

    def mark_processing(self) -> None:
        """Mark the event as currently being processed."""
        self.status = WebhookEventStatus.PROCESSING
        self.attempts += 1
        self.last_attempt_at = datetime.utcnow()

    def mark_completed(self, result: dict | None = None) -> None:
        """Mark the event as successfully processed."""
        self.status = WebhookEventStatus.COMPLETED
        self.processed_at = datetime.utcnow()
        if result:
            self.processing_result = result

    def mark_failed(self, error_message: str) -> None:
        """Mark the event as failed to process."""
        self.status = WebhookEventStatus.FAILED
        self.error_message = error_message
        self.last_attempt_at = datetime.utcnow()

    def mark_duplicate(self) -> None:
        """Mark the event as a duplicate."""
        self.status = WebhookEventStatus.DUPLICATE
        self.processed_at = datetime.utcnow()
