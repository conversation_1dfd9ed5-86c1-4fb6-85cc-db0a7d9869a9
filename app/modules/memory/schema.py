from __future__ import annotations

from datetime import datetime

from pydantic import BaseModel, Field

from .models import MemoryBase, MemoryCategoryBase, MemoryHistoryBase


# Memory schemas
class IMemoryRead(MemoryBase):
    id: str


class IMemoryCreate(BaseModel):
    memory_id: str
    agent_id: str | None = None
    user_id: str
    conversation_id: str | None = None
    category: str | None = None
    content: str
    embedding: list[float] | None = None
    links: list | None = None
    happened_at: datetime | None = None
    api_key_id: str | None = None
    project_id: str | None = None


class IMemoryUpdate(BaseModel):
    memory_id: str | None = None
    agent_id: str | None = None
    user_id: str | None = None
    category: str | None = None
    content: str | None = None
    embedding: list[float] | None = None
    links: list | None = None
    happened_at: datetime | None = None


# MemoryHistory schemas
class IMemoryHistoryRead(MemoryHistoryBase):
    id: str


class IMemoryHistoryCreate(BaseModel):
    memory_id: str
    agent_id: str | None = None
    user_id: str
    conversation_id: str | None = None
    action: str
    timestamp: datetime
    category: str | None = None
    content_before: str | None = None
    content_after: str | None = None
    links_before: list | None = None
    links_after: list | None = None


class IMemoryHistoryUpdate(BaseModel):
    memory_id: str | None = None
    agent_id: str | None = None
    user_id: str | None = None
    action: str | None = None
    timestamp: datetime | None = None
    category: str | None = None
    content_before: str | None = None
    content_after: str | None = None
    links_before: list | None = None
    links_after: list | None = None


# MemoryCategory schemas
class IMemoryCategoryRead(MemoryCategoryBase):
    id: str


class IMemoryCategoryCreate(BaseModel):
    agent_id: str | None = None
    user_id: str
    category_id: str | None = None
    group: str
    name: str
    description: str | None = None
    summary: dict | None = None
    count: int = 0
    project_id: str | None = None


class IMemoryCategoryUpdate(BaseModel):
    agent_id: str | None = None
    user_id: str | None = None
    category_id: str | None = None
    group: str | None = None
    name: str | None = None
    description: str | None = None
    summary: dict | None = None
    count: int | None = None


# API request/response schemas
class IMessageContent(BaseModel):
    role: str = Field(..., description="Message role: system, user, assistant, participant")
    name: str | None = Field(default=None, description="Speaker name, useful for participant")
    content: str = Field(..., description="Message content")


# class ISearchMemoryRequest(BaseModel):
#     user_id: str = Field(..., description="User ID")
#     user_name: str = Field(default="user", description="User name")
#     agent_id: str = Field(..., description="Agent ID")
#     agent_name: str = Field(default="agent", description="Agent name")
#     query: str = Field(..., description="Search query")


# class IUpdateMemoryRequest(BaseModel):
#     user_id: str = Field(..., description="User ID")
#     user_name: str = Field(default="user", description="User name")
#     agent_id: str = Field(..., description="Agent ID")
#     agent_name: str = Field(default="agent", description="Agent name")
#     conversation: list[IMessageContent] = Field(..., description="List of conversation messages")


class IMemorizeRequest(BaseModel):
    conversation_text: str | None = Field(default=None, description="Conversation text to memorize")
    conversation: list[IMessageContent] | None = Field(default=None, description="List of conversation messages")
    user_id: str = Field(..., description="User ID")
    user_name: str = Field(..., description="User name")
    agent_id: str = Field(..., description="Agent ID")
    agent_name: str = Field(..., description="Agent name")
    session_date: str | None = Field(default=None, description="Session date in ISO format")


class IMemorizeResponse(BaseModel):
    task_id: str = Field(..., description="Celery task ID for tracking the memorization process")
    status: str = Field(..., description="Task status")
    message: str = Field(..., description="Response message")


class IMemoryDeleteRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str | None = Field(default=None, description="Agent ID")


class IMemoryDeleteResponse(BaseModel):
    success: bool = Field(..., description="Whether the deletion was successful")
    deleted_count: int = Field(..., description="Number of memories deleted")


class IMemorySummaryReadyRequest(BaseModel):
    # user_id: str = Field(..., description="User ID")
    # agent_id: str = Field(..., description="Agent ID")
    # task_id: str = Field(..., description="Last task ID")
    group: str = Field(default="basic", description="Category group")


class IMemorySummaryReadyResponse(BaseModel):
    all_ready: bool = Field(..., description="Whether all summaries are ready")
    category_ready: dict[str, bool] = Field(..., description="Whether each category is ready")


# class IMemorySearchResult(BaseModel):
#     memory: IMemoryReadResult
#     similarity_score: float | None = None


# class ISearchMemoryResponse(BaseModel):
#     results: list[IMemorySearchResult]
#     total_found: int


# class IUpdateMemoryResponse(BaseModel):
#     created_memories: list[IMemoryReadResult]
#     updated_memories: list[IMemoryReadResult]
#     total_processed: int


# This is for internal use (e.g., web request). Do not use this for retrieve response
class IMemoryReadResult(BaseModel):
    id: str
    agent_id: str | None = None
    user_id: str
    conversation_id: str | None = None
    memory_id: str
    category: str | None = None
    content: str
    links: list | None = None
    happened_at: datetime | None = None
    created_at: datetime | None = None
    updated_at: datetime | None = None


# Retrieve API schemas


# Hide most details that should not be visible to users
class IMemoryRetrieveResult(BaseModel):
    memory_id: str
    category: str | None
    content: str
    happened_at: datetime | None
    created_at: datetime | None
    updated_at: datetime | None


# retrieve_default_categories
class IRetrieveDefaultCategoriesRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str | None = Field(default=None, description="Agent ID")
    include_inactive: bool = Field(default=False, description="Whether to include inactive categories")
    want_summary: bool = Field(default=True, description="Whether to request summary or raw memory items")


class ICategoryRetrieveResult(BaseModel):
    name: str
    type: str
    user_id: str
    agent_id: str | None = None
    description: str | None
    is_active: bool
    memories: list[IMemoryRetrieveResult] | None = None
    memory_count: int | None = None
    summary: str | None = None


class IRetrieveDefaultCategoriesResponse(BaseModel):
    categories: list[ICategoryRetrieveResult] = Field(..., description="List of default categories with their content")
    total_categories: int = Field(..., description="Total number of categories")
    # project_id: str = Field(..., description="Project ID")


# retrieve_related_memory_items
class IRetrieveRelatedMemoryItemsRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str | None = Field(default=None, description="Agent ID")
    query: str = Field(..., description="Query text for embedding search")
    top_k: int = Field(default=10, description="Number of related items to return")
    min_similarity: float = Field(default=0.3, description="Minimum similarity threshold")
    include_categories: list[str] | None = Field(default=None, description="Specific categories to search in")


class IRetrieveRelatedMemoryResult(BaseModel):
    memory: IMemoryRetrieveResult
    user_id: str
    agent_id: str | None = None
    similarity_score: float | None = None


class IRetrieveRelatedMemoryItemsResponse(BaseModel):
    related_memories: list[IRetrieveRelatedMemoryResult] = Field(
        ..., description="Related memory items from activities"
    )
    query: str = Field(..., description="Original query")
    total_found: int = Field(..., description="Total number of related memories found")
    search_params: dict = Field(..., description="Search parameters used")


# retrieve_related_clustered_categories
class IRetrieveRelatedClusteredCategoriesRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str | None = Field(default=None, description="Agent ID")
    category_query: str = Field(..., description="Category name or description to search for")
    top_k: int = Field(default=5, description="Number of top clustered categories to return")
    min_similarity: float = Field(default=0.3, description="Minimum category similarity threshold")


class IClusteredCategoryContent(BaseModel):
    name: str
    user_id: str
    agent_id: str | None = None
    similarity_score: float
    memories: list[IMemoryRetrieveResult] | None = None
    memory_count: int | None = None
    summary: str | None = None


class IRetrieveRelatedClusteredCategoriesResponse(BaseModel):
    clustered_categories: list[IClusteredCategoryContent] = Field(
        ..., description="Related clustered categories with content"
    )
    category_query: str = Field(..., description="Original category query")
    total_categories_found: int = Field(..., description="Total number of related categories found")
    search_params: dict = Field(..., description="Search parameters used")


# web frontend schemas


# Agent search schemas
class IAgentSearchRequest(BaseModel):
    agent__icontains: str | None = Field(default=None, description="Filter agents by partial agent_id match")


class IAgentSearchResponse(BaseModel):
    agent_id: str = Field(..., description="Agent ID")
    agent_name: str | None = Field(default=None, description="Agent name")


class IAgentUsersSearchRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID to search users for")
    user__icontains: str | None = Field(default=None, description="Filter users by partial user_id match")


class IAgentUsersSearchResponse(BaseModel):
    user_id: str = Field(..., description="User ID")
    user_name: str | None = Field(default=None, description="User name")


class IMemoryCategoriesSearchRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str = Field(..., description="Agent ID")
    category__icontains: str | None = Field(
        default=None, description="Filter categories by partial category_name match"
    )


class IMemoryCategoriesSearchResponse(BaseModel):
    name: str = Field(..., description="Memory category name")
    count: int = Field(..., description="Number of memories in this category")
    created_at: datetime | None = Field(default=None, description="Creation timestamp of the category")
    updated_at: datetime | None = Field(default=None, description="Last updated timestamp of the category")
    length: int = 12 * 1000


class IMemoryCategorySummaryRequest(BaseModel):
    user_id: str = Field(..., description="User ID")
    agent_id: str = Field(..., description="Agent ID")
    name: str = Field(..., description="Memory category name to summarize")


class IMemoryCategorySummaryResponse(BaseModel):
    name: str = Field(..., description="Memory category name")
    # count: int = Field(..., description="Number of memories in this category")
    content: str = Field(..., description="Content of the memory category")
