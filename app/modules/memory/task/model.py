from datetime import datetime
from enum import Enum

from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>
from sqlmodel import Column, DateTime, Field, Relationship, SQLModel

from app.modules.conversation.models import Conversation
from app.modules.shared.base_model import BaseModelMixin


class TaskStatus(str, Enum):
    """Task status enumeration"""

    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    CANCELLED = "CANCELLED"


class MemoryTaskBase(SQLModel):
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Task status")
    detail_info: str | None = Field(default=None, description="Task detail information")
    finished_at: datetime | None = Field(default=None, description="Task finished at", sa_type=DateTime(timezone=True))
    token_used: int | None = Field(default=None, description="Token used")
    conversation_id: str | None = Field(
        default=None, foreign_key="conversations.id", index=True, description="Conversation ID"
    )
    debug_info: dict | None = Field(default=None, sa_column=Column(JSONB), description="Debug information")


class MemoryTask(BaseModelMixin, MemoryTaskBase, table=True):
    __tablename__ = "memory_celery_tasks"

    conversation: "Conversation" = Relationship()
