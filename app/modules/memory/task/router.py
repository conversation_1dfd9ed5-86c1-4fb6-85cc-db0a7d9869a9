from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException
from fastapi_pagination import Params
from sqlalchemy.orm import selectinload
from sqlmodel import or_, select

from app.modules.conversation.models import Conversation
from app.modules.memory.models import MemoryHistory
from app.modules.memory.task.crud import MemoryTaskRepo
from app.modules.memory.task.model import MemoryTask
from app.modules.memory.task.schema import (
    IMemoryDiff,
    IMemoryDiffEnhanced,
    ITaskResponse,
    ITaskSearchRequest,
    ITaskStatusResponseFull,
)
from app.modules.permission.deps import need_proj_permission
from app.modules.shared.base_policy import Action
from app.modules.shared.base_schema import IGetResponsePaginated, create_response
from app.services.db.deps import DatabaseSessionDep

resource = "memory"
CAN_CREATE = need_proj_permission(Action.CREATE, resource)
CAN_READ = need_proj_permission(Action.READ, resource)
CAN_UPDATE = need_proj_permission(Action.UPDATE, resource)
CAN_DELETE = need_proj_permission(Action.DELETE, resource)

router = APIRouter(prefix="/projects/{project_id}", tags=["memory-tasks"])


@router.post("/tasks/search")
async def search_all_tasks(
    *,
    project_id: str,
    params: Annotated[Params, Depends()],
    request: ITaskSearchRequest,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Get all tasks for a project
    """

    try:
        query = (
            select(MemoryTask)
            .join(Conversation)
            .options(selectinload(MemoryTask.conversation))
            .order_by(MemoryTask.created_at.desc())
        )

        if request.content__icontains:
            # Join with Conversation table and search in multiple fields
            query = query.where(
                or_(
                    MemoryTask.id.ilike(f"%{request.content__icontains}%"),
                    Conversation.agent_id.ilike(f"%{request.content__icontains}%"),
                    Conversation.agent_name.ilike(f"%{request.content__icontains}%"),
                    Conversation.user_id.ilike(f"%{request.content__icontains}%"),
                    Conversation.user_name.ilike(f"%{request.content__icontains}%"),
                )
            )
        query = query.where(Conversation.project_id == project_id)

        task_result = await MemoryTaskRepo.get_multi_paginated(
            query=query,
            params=params,
            db_session=db_session,
        )

        tasks = task_result.items

        task_items = [
            ITaskResponse(
                task_id=task.id,
                status=task.status,
                detail_info=task.detail_info,
                token_used=task.token_used,
                created_at=task.created_at,
                updated_at=task.updated_at,
                finished_at=task.finished_at,
                conversation_id=task.conversation_id,
                user_id=task.conversation.user_id if task.conversation else None,
                user_name=task.conversation.user_name if task.conversation else None,
                agent_id=task.conversation.agent_id if task.conversation else None,
                agent_name=task.conversation.agent_name if task.conversation else None,
            )
            for task in tasks
        ]

        return IGetResponsePaginated.create(items=task_items, total=task_result.total, params=params)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task list: {e!s}")


# Wu: when the query directly provided the task_id (query for single task),
#     return all details including diff
@router.get("/tasks/{task_id}")
async def get_task_by_id(
    *,
    project_id: str,
    task_id: str,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Get task detail by task ID
    """
    try:
        task = await MemoryTaskRepo.get_by_task_id(task_id=task_id, db_session=db_session)

        if not task:
            raise HTTPException(status_code=500, detail=f"Task id {task_id} not found")

        _finished = task.status == "SUCCESS"
        if _finished:
            conversation_id = task.conversation_id

            query = (
                select(MemoryHistory)
                .where(MemoryHistory.conversation_id == conversation_id)
                .order_by(MemoryHistory.created_at)
            )

            result = await db_session.execute(query)
            memory_history = result.scalars().all()

            memory_diffs = []
            for memory_history_item in memory_history:
                memory_diff = IMemoryDiffEnhanced(
                    memory_id=memory_history_item.memory_id,
                    action=memory_history_item.action,
                    category=memory_history_item.category,
                    content_before=memory_history_item.content_before,
                    content_after=memory_history_item.content_after,
                    links_before=memory_history_item.links_before,
                    links_after=memory_history_item.links_after,
                )

                memory_diffs.append(memory_diff)
        else:
            memory_diffs = None

        task_response = ITaskStatusResponseFull(
            task_id=task.id,
            status=task.status,
            detail_info=task.detail_info,
            token_used=task.token_used,
            created_at=task.created_at,
            updated_at=task.updated_at,
            finished_at=task.finished_at,
            conversation_id=task.conversation_id,
            user_id=task.conversation.user_id if task.conversation else None,
            user_name=task.conversation.user_name if task.conversation else None,
            agent_id=task.conversation.agent_id if task.conversation else None,
            agent_name=task.conversation.agent_name if task.conversation else None,
            diff=memory_diffs,
        )

        return create_response(data=task_response)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task detail: {e!s}")


@router.get("/tasks/{task_id}/diff")
async def get_task_diff(
    *,
    project_id: str,
    task_id: str,
    _=CAN_READ,
    db_session: DatabaseSessionDep,
):
    """
    Get task diff by task ID
    """
    try:
        task = await MemoryTaskRepo.get_by_task_id(task_id=task_id, db_session=db_session)

        if not task:
            raise HTTPException(status_code=500, detail=f"Task id {task_id} not found")

        conversation_id = task.conversation_id

        if not conversation_id:
            raise HTTPException(status_code=500, detail=f"Conversation id not found for task id {task_id}")

        query = (
            select(MemoryHistory)
            .where(MemoryHistory.conversation_id == conversation_id)
            .order_by(MemoryHistory.created_at)
        )

        result = await db_session.execute(query)
        memory_history = result.scalars().all()

        memory_diffs = []
        for memory_history_item in memory_history:
            if (
                memory_history_item.action == "UPDATE"
                and memory_history_item.content_before == memory_history_item.content_after
            ):
                # Wu: only link update, no need to show to users
                continue

            memory_diff = IMemoryDiff(
                memory_id=memory_history_item.memory_id,
                action=memory_history_item.action,
                category=memory_history_item.category,
                content_before=memory_history_item.content_before,
                content_after=memory_history_item.content_after,
            )

            memory_diffs.append(memory_diff)

        # Group memory diffs by category
        grouped_diffs = {}
        for diff in memory_diffs:
            category = diff.category
            if category not in grouped_diffs:
                grouped_diffs[category] = []
            grouped_diffs[category].append(diff)

        return create_response(
            data=[{"category": category, "diffs": diffs} for category, diffs in grouped_diffs.items()]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task diff: {e!s}")
