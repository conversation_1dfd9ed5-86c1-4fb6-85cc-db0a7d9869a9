import asyncio
import math
from datetime import datetime
from typing import Any

import pendulum

from app.modules.conversation.crud import ConversationRepo
from app.modules.conversation.schema import IConversationCreate
from app.modules.memory.task.crud import MemoryTaskRepo
from app.services.db.session import SessionFactory
from app.services.task_queue import celery_app
from app.tasks.memory.agent.actions.base_action import ActionContext
from app.tasks.memory.agent.memory_agent import MemoryAgent
from app.tasks.memory.llm.llm_factory import get_llm_client
from app.tasks.memory.llm.tokenize import get_token_count
from app.tasks.memory.utils import get_logger
from config.loader import SettingsFactory

logger = get_logger(__name__)


def convert_conversation_text_to_agent_format(
    conversation_text: str, user_name: str, agent_name: str, allow_custom: bool = False
) -> list[dict[str, str]]:
    """
    Convert conversation text to the format expected by MemoryAgent
    """
    # Simple parsing - try to detect user/agent turns
    messages = []
    lines = conversation_text.strip().split("\n")

    current_content = []
    current_role = None

    def add_message():
        if current_role in ["user", "assistant"]:
            messages.append({"role": current_role, "content": " ".join(current_content).strip()})
        else:
            messages.append({"role": "participant", "name": current_role, "content": " ".join(current_content).strip()})

    for line in lines:
        line = line.strip()
        if not line:
            continue

        line_lower = line.lower()

        # Try to detect role based on line content
        if line_lower.startswith("user:") or line_lower.startswith(user_name.lower()):
            # Save previous message
            if current_role and current_content:
                add_message()
            # Start new user message
            current_role = "user"
            if line_lower.startswith("user:"):
                current_content = [line[5:].strip()]
            else:
                current_content = [line[len(user_name) :].strip()]

        elif line_lower.startswith("assistant:") or line_lower.startswith(agent_name.lower()):
            # Save previous message
            if current_role and current_content:
                add_message()
            # Start new assistant message
            current_role = "assistant"
            if line_lower.startswith("assistant:"):
                current_content = [line[10:].strip()]
            else:
                current_content = [line[len(agent_name) :].strip()]

        # Suppose name is not longer than 10 characters
        elif allow_custom and ": " in line[:12]:
            custom_role, custom_content = line.split(":", 1)
            current_role = custom_role.strip()
            current_content = [custom_content.strip()]

        else:
            # Continue current message
            if current_content:
                current_content.append(line)
            else:
                # No role detected yet, default to user
                current_role = "user"
                current_content = [line]

    # Add final message
    if current_role and current_content:
        add_message()

    # If no structured messages found, treat entire conversation as user message
    if not messages:
        messages.append({"role": "user", "content": conversation_text})

    return messages


def convert_conversation_role(
    conversation: list[dict[str, Any]], user_name: str, agent_name: str, allow_custom: bool = True
) -> list[dict[str, str]]:
    """
    Convert conversation role to the format expected by MemoryAgent
    """
    messages = []

    for message in conversation:
        if not message.get("role") or not message.get("content"):
            continue

        while "\n\n" in message["content"]:
            message["content"] = message["content"].replace("\n\n", "\n")

        role_lower = message["role"].lower()

        if role_lower == "user" or role_lower == user_name.lower():
            messages.append({"role": "user", "content": message["content"]})

        elif role_lower == "assistant" or role_lower == agent_name.lower():
            messages.append({"role": "assistant", "content": message["content"]})

        elif allow_custom:
            messages.append({"role": "participant", "name": message["role"], "content": message["content"]})

        else:
            logger.warning(f"Unknown role: {message['role']}, fallback to user")
            continue
            # messages.append({"role": "user", "content": message["content"]})

    return messages


def get_conversation_token_count(conversation: list[dict[str, Any]]) -> int:
    """
    Get token count
    """
    return sum(get_token_count(message["content"]) for message in conversation)


def break_long_message(conversation: list[dict[str, Any]]) -> list[dict[str, Any]]:
    """
    Break long message into multiple messages
    """
    max_message_words = SettingsFactory.settings().MAX_MESSAGE_WORDS

    processed_conversation = []
    for message in conversation:
        if len(message["content"].split()) > max_message_words:
            message_words = message["content"].split(" ")
            for i in range(0, len(message_words), max_message_words):
                processed_conversation.append({
                    "role": message["role"],
                    "content": " ".join(message_words[i : i + max_message_words]),
                })
        else:
            processed_conversation.append(message)
    return processed_conversation


def get_conversation_batch(
    conversation: list[dict[str, Any]], min_messages_in_batch: int = 10, overlap_messages: int = 0
) -> list[list[dict[str, Any]]]:
    """
    Get conversation batch
    """
    batch_word_size = SettingsFactory.settings().MEMORIZE_BATCH_WORDS
    message_lengths = [len(message["content"].split()) for message in conversation]

    batches = []
    current_batch = []
    batch_start = 0
    batch_word_count = 0
    for i in range(len(message_lengths)):
        current_batch.append(conversation[i])
        batch_word_count += message_lengths[i]

        if i - batch_start + 1 >= min_messages_in_batch and batch_word_count >= batch_word_size:
            batches.append(current_batch)
            current_batch = []
            if overlap_messages > 0:
                current_batch = current_batch[max(0, i - overlap_messages) : i + 1]
                batch_word_count = sum(message_lengths[max(0, i - overlap_messages) : i + 1])
            else:
                batch_word_count = 0
            batch_start = i + 1

    if current_batch:
        batches.append(current_batch)

    return batches


@celery_app.task(name="memorize", bind=True)
def memorize_conversation(
    self,
    conversation: dict[str, Any],
    user_id: str,
    user_name: str,
    agent_id: str,
    agent_name: str,
    session_date: str | None = None,
    api_key_id: str | None = None,
    project_id: str | None = None,
):
    """
    Celery task to process and memorize conversation text using MemoryAgent.
    All data is stored directly to database without temporary files.

    Args:
        conversation_text: The conversation text to memorize
        user_id: User ID
        user_name: User name
        agent_id: Agent ID
        agent_name: Agent name
        session_date: Session date

    Returns:
        dict: Result of the memorization process
    """
    try:
        logger.info(f"Starting memorization task for user {user_id} and agent {agent_id}")

        task_id = self.request.id

        # Update progress
        self.update_state(
            state="PROCESSING", meta={"current": 20, "total": 100, "status": "Converting conversation format..."}
        )
        asyncio.run(
            MemoryTaskRepo.update_task_state_detached(
                task_id=task_id, status="PROCESSING", detail_info="Converting conversation format..."
            )
        )

        # Step 1: Save conversation to database
        session_date = session_date or datetime.now().isoformat()

        if conversation["formatted"]:
            conversation_messages = convert_conversation_role(conversation["messages"], user_name, agent_name)
        else:
            conversation_messages = convert_conversation_text_to_agent_format(
                conversation["text"], user_name, agent_name
            )

        settings = SettingsFactory.settings()
        conversation_priced_token = get_conversation_token_count(conversation_messages)
        conversation_mtoken_price = math.ceil(conversation_priced_token / settings.MEMORIZE_TOKEN_PRICE)

        self.update_state(
            state="PROCESSING", meta={"current": 5, "total": 100, "status": "Saving conversation to database..."}
        )
        asyncio.run(
            MemoryTaskRepo.update_task_state_detached(task_id=task_id, detail_info="Saving conversation to database...")
        )

        async def save_conversation():
            async with SessionFactory.make_local_session() as session:
                # Create conversation record
                conversation_create = IConversationCreate(
                    user_id=user_id,
                    user_name=user_name,
                    agent_id=agent_id,
                    agent_name=agent_name,
                    title=f"Conversation with {agent_name}",
                    session_date=session_date,
                    message_count=len(conversation_messages),
                    content=conversation_messages,
                    api_key_id=api_key_id,
                    project_id=project_id,
                )

                db_conversation = await ConversationRepo.create(new=conversation_create, db_session=session)
                await session.commit()

                logger.info(
                    f"Saved conversation length: {len(conversation_messages)} to database with ID: {db_conversation.id}"
                )
                return db_conversation.id

        # Run async conversation saving
        conversation_id = asyncio.run(save_conversation())

        # Update task status
        self.update_state(
            state="PROCESSING", meta={"current": 15, "total": 100, "status": "Initializing memory agent..."}
        )
        asyncio.run(
            MemoryTaskRepo.update_task_state_detached(
                task_id=task_id, conversation_id=conversation_id, detail_info="Initializing memory agent..."
            )
        )

        context = ActionContext(
            user_id=user_id,
            user_name=user_name,
            agent_id=agent_id,
            agent_name=agent_name,
            conversation_id=conversation_id,
            conversation=conversation_messages,
            session_date=session_date,
            api_key_id=api_key_id,
            project_id=project_id,
        )

        # Initialize LLM client and Memory Agent (no file system needed)
        try:
            llm_client = get_llm_client()
            embedding_client = get_llm_client(task="embedding")

            memory_agent = MemoryAgent(
                llm_client=llm_client,
                embedding_client=embedding_client,
                action_context=context,
            )

            # Wu: put a asyncio.run here may be not a good idea, consider a better solution later
            init_jobs_result = asyncio.run(memory_agent.memory_core.memory_repo.do_init_jobs())
            if not init_jobs_result["success"]:
                logger.error(f"Failed to initialize memory repository: {init_jobs_result['errors']}")
                raise Exception(f"Failed to initialize memory repository: {init_jobs_result['errors']}")
        except Exception as e:
            logger.exception(f"Failed to initialize memory agent: {e}")
            raise

        # Update progress
        self.update_state(
            state="PROCESSING", meta={"current": 30, "total": 100, "status": "Processing with memory agent..."}
        )
        asyncio.run(
            MemoryTaskRepo.update_task_state_detached(task_id=task_id, detail_info="Processing with memory agent...")
        )

        conversation_break = break_long_message(conversation_messages)
        conversation_batches = get_conversation_batch(conversation_break)

        result = {
            "agent_iterations": [],
            "agent_function_calls": [],
            "agent_results": [],
            "token_usages": [],
            "time_use": [],
        }
        for i, conversation in enumerate(conversation_batches):
            # Process conversation with memory agent using new database parameters
            async def process_with_memory_agent():
                return await memory_agent.run(
                    conversation=conversation,
                    user_id=user_id,
                    agent_id=agent_id,
                    conversation_id=conversation_id,
                    # user_name=user_name,
                    user_name="User",
                    # agent_name=agent_name,
                    agent_name="Assistant",
                    session_date=session_date,
                    max_iterations=20,
                )

            # Run async memory agent processing
            agent_result = asyncio.run(process_with_memory_agent())

            result["agent_iterations"].append(agent_result.get("iterations", 0))
            result["agent_function_calls"].append(agent_result.get("function_calls", []))
            result["agent_results"].append(
                {"success": True}
                if agent_result.get("success")
                else {"success": False, "error": agent_result.get("error")}
            )
            result["token_usages"].append(agent_result.get("token_usage", {}))
            result["time_use"].append(agent_result.get("time_use", []))

        # Update progress
        self.update_state(state="PROCESSING", meta={"current": 90, "total": 100, "status": "Finalizing..."})
        asyncio.run(MemoryTaskRepo.update_task_state_detached(task_id=task_id, detail_info="Finalizing..."))

        result["operation_record"] = {
            operation: list(record)
            for operation, record in memory_agent.memory_core.memory_repo.operation_record.items()
        }
        result["memory_category_touched"] = memory_agent.memory_core.memory_repo.memory_category_touched
        result["memory_histories_created"] = memory_agent.memory_core.memory_repo.memory_history_created

        result["llm_record"] = memory_agent.memory_core.llm_client.get_simplified_record()
        result["total_token_usage"] = (
            result["token_usages"][0]
            if len(result["token_usages"]) == 1
            else get_total_token_usage(result["token_usages"])
        )

        post_jobs_result = asyncio.run(memory_agent.memory_core.memory_repo.do_post_jobs())
        if not post_jobs_result["success"]:
            logger.error(f"Failed to finalize memory repository: {post_jobs_result['errors']}")
            raise Exception(f"Failed to finalize memory repository: {post_jobs_result['errors']}")

        # Prepare result
        celery_result = result | {
            "status": "completed",
            "processing_method": "conversation_first_then_memory_agent",
            "conversation_id": conversation_id,
            "conversation_length": len(conversation_messages),
            "user_id": user_id,
            "user_name": user_name,
            "agent_id": agent_id,
            "agent_name": agent_name,
            "timestamp": datetime.now().isoformat(),
        }

        logger.info(
            f"Memorization completed for user {user_id} - agent {agent_id}"
            f"agent processed in {sum(result['agent_iterations'])} iterations, "
            f"performed {sum(len(call_records) for call_records in result['agent_function_calls'])} function calls in total, "
            f"performed {len(result['memory_histories_created'])} memory operations in total, "
            # f"created {sum(len(agent_result['memory_histories_created']) for agent_result in result['agent_result'])} memory history records"
        )

        self.update_state(
            state="SUCCESS", meta={"current": 100, "total": 100, "status": "Memorization completed successfully"}
        )
        asyncio.run(
            MemoryTaskRepo.update_task_state_detached(
                task_id=task_id,
                status="SUCCESS",
                detail_info="Memorization completed successfully",
                finished_at=pendulum.now("UTC"),
                token_used=conversation_mtoken_price,
            )
        )
        if settings.DUMP_DEBUG_INFO:
            safe_dump_debug_info(task_id, result)

        from app.tasks.summarize import summarize_memory as summarize_task

        task = summarize_task.apply_async(
            args=[
                user_id,
                agent_id,
                project_id,
                list(memory_agent.memory_core.memory_repo.memory_category_touched),
            ],
        )

        return celery_result

    except Exception as exc:
        logger.exception(f"Memorization task failed: {exc}")
        # Prepare JSON-serializable error information
        error_info = {
            "error_type": type(exc).__name__,
            "error_message": str(exc),
            "current": 100,
            "total": 100,
            "status": f"Task failed: {exc!s}",
        }
        self.update_state(state="FAILURE", meta=error_info)
        asyncio.run(
            MemoryTaskRepo.update_task_state_detached(
                task_id=task_id, status="FAILURE", detail_info=f"Memorization task failed: {exc!s}"
            )
        )
        # Don't re-raise, let Celery handle the original exception naturally
        raise exc


def get_total_token_usage(token_usages: list[dict[str, dict[str, int]]]) -> dict[str, dict[str, int]]:
    """
    Get total token usage
    """
    total_usage = {}
    for agent_usage in token_usages:
        for client_name, client_usage in agent_usage.items():
            if client_name in total_usage:
                for key, value in client_usage.items():
                    if key in total_usage[client_name]:
                        total_usage[client_name][key] += value
                    else:
                        total_usage[client_name][key] = value
            else:
                total_usage[client_name] = client_usage
    return total_usage


def safe_dump_debug_info(task_id: str, result: dict[str, Any]) -> bool:
    """
    Safe dump debug info
    """
    try:
        asyncio.run(MemoryTaskRepo.update_task_state_detached(task_id=task_id, debug_info=result))
    except Exception as e1:
        logger.exception(f"Failed to dump debug info: {e1!r}")
        logger.exception("Fallback to dump as string")
        try:
            asyncio.run(MemoryTaskRepo.update_task_state_detached(task_id=task_id, debug_info={"repr": repr(result)}))
        except Exception as e2:
            logger.exception(f"Failed to dump debug info: {e2!r}")
            return False
    return True
