import itertools
from datetime import datetime
from typing import Any

from app.modules.memory.models import Memory

from ...utils import get_logger
from .base_action import BaseAction

logger = get_logger(__name__)


class ClusterMemoriesAction(BaseAction):
    """
    Cluster memories into different categories.
    """

    def __init__(self, memory_core):
        super().__init__(memory_core)
        self.description = "Cluster memories into different categories"

        self.detect_retrieve_existing_each = 10
        self.detect_retrieve_existing_max = 100

    @property
    def action_name(self) -> str:
        return "cluster_memories"

    def get_schema(self) -> dict[str, Any]:
        """Return OpenAI-compatible function schema"""
        return {
            "name": self.action_name,
            "description": "Cluster memories into different categories",
            "parameters": {
                "type": "object",
                "properties": {
                    # "conversation_text": {
                    #     "type": "string",
                    #     "description": "Complete original conversation text exactly as provided - do NOT modify, extract, or summarize",
                    # },
                    "new_memory_items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "memory_id": {"type": "string"},
                                "content": {"type": "string"},
                                "mentioned_at": {"type": "string"},
                            },
                            "required": ["memory_id", "content", "mentioned_at"],
                        },
                        "description": "List of new memory items generated from the conversation",
                    },
                },
                "required": ["new_memory_items"],
            },
        }

    async def execute(
        self,
        *,
        conversation_text: str = "",
        new_memory_items: list[dict[str, str]],
        new_theory_of_mind_items: list[dict[str, str]] = [],
        # available_categories: List[str]
        session_date: str | None = None,
    ) -> dict[str, Any]:
        """
        Cluster memories into different categories
        """
        try:
            if not session_date:
                session_date = self.action_context.session_date or datetime.now().strftime("%Y-%m-%d")

            basic_categories = await self.memory_repo.get_memory_categories("basic")
            basic_categories = [category.name for category in basic_categories]
            existing_clusters = await self.memory_repo.get_memory_categories("cluster")
            existing_clusters = [category.name for category in existing_clusters]

            logger.info(f"Existing clusters: {existing_clusters}")

            all_candidate_items = {
                item["memory_id"]: item for item in itertools.chain(new_memory_items, new_theory_of_mind_items)
            }

            # Try to merge to existing clusters if exist
            updated_clusters = {}
            if existing_clusters:
                merge_result = await self._merge_existing_clusters(
                    existing_clusters, conversation_text, all_candidate_items
                )

                # logger.info(f"Merge result: {merge_result!r}")

                if not merge_result.get("success"):
                    return self._add_metadata(merge_result)

                updated_clusters = merge_result.get("updated_clusters", {})

            # Try to detect new clusters
            new_cluster_result = await self._detect_new_clusters(
                basic_categories, existing_clusters, conversation_text, all_candidate_items
            )

            # logger.info(f"New cluster result: {repr(new_cluster_result)}")

            if not new_cluster_result.get("success"):
                return self._add_metadata(new_cluster_result)

            new_clusters = new_cluster_result.get("new_clusters", {})

            # logger.info(f"New clusters: {repr(new_clusters)}")

            for cluster, memory_ids in itertools.chain(updated_clusters.items(), new_clusters.items()):
                if not memory_ids:
                    continue

                # logger.info(f"Processing cluster: {cluster}, {memory_ids}")

                # Wu: we told LLM not to recreate existing clusters, but sometimes it still does.
                if cluster not in existing_clusters:
                    await self.memory_repo.new_memory_cluster(cluster_name=cluster)

                # Wu: need detect existing if duplicated categories
                for memory_id in memory_ids:
                    original_item = all_candidate_items[memory_id]
                    original_memory_id = original_item["memory_id"]
                    # new_memory_id = self._generate_memory_id()

                    memory_item = {
                        "memory_id": original_memory_id,
                        "mentioned_at": original_item.get("mentioned_at", session_date),
                        "content": original_item["content"],
                        "links": [original_memory_id],
                    }

                    await self.memory_repo.add_memory_item(cluster, memory_item)

            return self._add_metadata({
                "success": True,
                "updated_clusters": sorted(updated_clusters.keys()),
                "new_clusters": sorted(new_clusters.keys()),
                "message": f"Analyzed {len(new_memory_items)} new memory items. Updated {len(updated_clusters)} existing clusters and detected {len(new_clusters)} new clusters",
            })
        except Exception as e:
            return self._handle_error(e)

    async def _merge_existing_clusters(
        self,
        existing_clusters: list[str],
        conversation_text: str,
        candidate_items: list[dict[str, str]],
        # count_threshold: int = 3
    ) -> list[str]:
        """
        Merge existing clusters with new memory items and theory of mind items
        """

        memory_items_flattened = "\n".join([
            # f"Memory ID: {item['memory_id']}\nContent: {item['content']}" for item in candidate_items.values()
            f"[Memory ID: {item['memory_id']}] {item['content']}"
            for item in candidate_items.values()
        ])

        system_message = f"""You are an expert in analyzing and categorizing memories items.

You are given a list of existing clusters, a list of memory items, and the full conversation context that generated these memories.
Your task is to analyze if each of the memory items is related to any of the existing clusters.

**CONVERSATION CONTEXT:**
{conversation_text}

**EXISTING CLUSTERS:**
{"\n".join([f"- {cluster}" for cluster in existing_clusters])}

**MEMORY ITEMS:**
{memory_items_flattened}

**INSTRUCTIONS:**
1. Use the conversation context to better understand the background and relationships of the memory items.
2. It is possible that a memory item is related to multiple clusters.
Example: "We went to hiking in Blue Ridge Mountains this summer" is related to both "hiking" and "summer events" clusters, if both these two clusters are in the Existing Clusters.
3. If it possible that some memory items are not related to any of the existing clusters, you don't need to force them into any cluster.
4. DO NOT output memory items that are not related to any of the existing clusters.
5. Consider the conversation context when determining relationships - topics discussed together might belong to the same cluster.

**OUTPUT FORMAT:**
- [Memory ID]: [Cluster names that the memory item is related to, separated by comma]
- [Memory ID]: [Cluster names that the memory item is related to, separated by comma]
- ...
"""

        response = self.llm_client.simple_chat(system_message)

        if not response.strip():
            return {"success": False, "error": "LLM returned empty response for merging existing clusters"}

        updated_clusters = {}

        for line in response.split("\n"):
            if not line.startswith("- "):
                continue

            line = line.replace("Memory ID: ", "").replace("Memory ID", "")
            line = line.replace("[", "").replace("]", "")

            memory_id, clusters = line[2:].split(": ", 1)
            memory_id = memory_id.strip()

            if memory_id not in candidate_items:
                continue

            for cluster in clusters.split(","):
                cluster = cluster.strip().lower()
                if cluster not in existing_clusters:
                    continue
                if cluster not in updated_clusters:
                    updated_clusters[cluster] = []
                updated_clusters[cluster].append(memory_id)

        return {"success": True, "updated_clusters": updated_clusters, "raw_response": response}

    async def _detect_new_clusters(
        self,
        basic_categories: list[str],
        existing_clusters: list[str],
        conversation_text: str,
        candidate_items: list[dict[str, str]],
        count_threshold: int = 3,
    ) -> list[str]:
        """
        Detect new clusters from new memory items and theory of mind items
        """

        memory_items_flattened = "\n".join([
            # f"Memory ID: {item['memory_id']}\nContent: {item['content']}" for item in candidate_items.values()
            f"[Memory ID: {item['memory_id']}] {item['content']}"
            for item in candidate_items.values()
        ])

        memory_items_related = await self.select_top_k_related(
            from_categories=basic_categories,
            references=candidate_items.values(),
            k_each=self.detect_retrieve_existing_each,
            k_total=self.detect_retrieve_existing_max,
        )
        memory_items_related_flattened = "\n".join([
            f"[Memory ID: {item.memory_id}] {item.content}" for item in memory_items_related
        ])

        excluded_categories = basic_categories + existing_clusters

        system_message = f"""You are an expert in discovering some important or repeating events in one's memory records.

You are given a conversation context, a list of memory items extracted from this conversation, existing events/themes clusters, and some other possibly related memory items (not directly from the conversation).
Your task is to discover NEW events/themes that are either:
- Important (e.g., marriage, job promotion, etc.), or
- Repeating, periodical, or routine (e.g., going to gym, attending specific events, etc.).

**CONVERSATION CONTEXT:**
{conversation_text}

**EXISTING CLUSTERS (DO NOT recreate these):**
{"\n".join(f"- {cluster}" for cluster in excluded_categories)}

**MEMORY ITEMS (FROM CONVERSATION):**
{memory_items_flattened}

**MEMORY ITEMS (POSSIBLY RELATED):**
{memory_items_related_flattened}

**INSTRUCTIONS:**
1. Use the conversation context to better understand the significance and relationships of events.
2. Only create NEW clusters - do not recreate existing clusters listed above.
3. You should create a Event Name for each NEW event you discover.
4. The Event Name should be short and clear. A single word is the best (e.g., "marriage", "hiking"). Never let the name be longer than 3 words.
5. The Event Name should contains only alphabets or space. DO NOT use any other characters including hyphen, underscore, etc.
6. An event can be considered repeating, periodical, or routine, if they are mentioned at least {count_threshold} times in the memory items OR if the conversation context suggests it's a recurring theme.
7. If an event is considered important enough to one's life (e.g., proposal), you should record it regardless of how many times it is mentioned.
8. For event/theme content that are close (e.g., hiking and backpacking), you can merge them into a single event/theme, and accumulate the count. You also don't need to create a new cluster if there is already a cluster semantically close enough in the existing clusters.
9. Consider the conversation flow - events discussed together might indicate related themes or patterns.

**OUTPUT FORMAT:**
- [Event Name]: [Memory ID of ALL memory items related to this event, separated by comma. ONLY the ID, NO NEED to output "Memory ID: "]
- [Event Name]: [Memory ID of ALL memory items related to this event, separated by comma]
- ...
"""

        response = self.llm_client.simple_chat(system_message)

        # logger.info(f"System message: {system_message}")
        # logger.info(f"LLM response: {response}")

        if not response.strip():
            return {"success": False, "error": "LLM returned empty response for detecting new clusters"}

        new_clusters = {}

        for line in response.split("\n"):
            if not line.startswith("- "):
                continue

            line = line.replace("Memory ID: ", "").replace("Memory ID", "")
            line = line.replace("[", "").replace("]", "")

            cluster, memory_ids = line[2:].split(": ", 1)
            cluster = cluster.strip().lower()

            if cluster in basic_categories:
                continue

            if cluster not in new_clusters:
                new_clusters[cluster] = []

            for memory_id in memory_ids.split(","):
                memory_id = memory_id.strip()
                if memory_id not in candidate_items:
                    continue

                new_clusters[cluster].append(memory_id)

        return {"success": True, "new_clusters": new_clusters, "raw_response": response}

    async def select_top_k_related(
        self,
        *,
        from_categories: list[str] = None,
        references: list[dict[str, str]],
        k_each: int,
        k_total: int,
    ) -> list[Memory]:
        """
        Select top k memories most related to the suggestion based on cosine similarity.
        For each memory, calculate its maximum cosine similarity with any reference vector from suggestion.
        """
        # Generate embeddings for each line in suggestion as reference vectors

        if from_categories is None:
            basic_categories = await self.memory_repo.get_memory_categories("basic")
            from_categories = [category.name for category in basic_categories]
        if "activity" in from_categories:
            from_categories.remove("activity")

        collection = {}

        for memory_item in references:
            if not memory_item["content"]:
                continue

            related_items = await self.memory_repo.similarity_search(
                query=memory_item["content"],
                categories=from_categories,
                limit=k_each,
                similarity_threshold=0,
            )

            for related_item, similarity in related_items:
                if (
                    related_item.memory_id != memory_item["memory_id"]
                    and related_item.content != memory_item["content"]
                ):
                    if related_item.id in collection:
                        if similarity > collection[related_item.id][1]:
                            collection[related_item.id] = (related_item, similarity)
                    else:
                        collection[related_item.id] = (related_item, similarity)

        collection = sorted(collection.values(), key=lambda x: x[1], reverse=True)

        if len(collection) > k_total:
            collection = collection[:k_total]

        return [item[0] for item in collection]
