from dataclasses import dataclass

import pendulum
from sqlmodel.ext.asyncio.session import AsyncSession

from app.modules.memory.crud import MemoryCategoryRepo, MemoryRepo
from app.modules.memory.schema import IMemoryCategoryUpdate
from app.modules.organization.project.memory_category.crud import ProjectMemCategoryRepo
from app.services.db.session import SessionFactory
from app.tasks.memory.llm.llm_factory import BaseLLMClient
from app.tasks.memory.llm.tokenize import get_token_count
from app.tasks.memory.utils import get_logger
from config.loader import SettingsFactory

logger = get_logger(__name__)


@dataclass
class SummarizationDependency:
    user_id: str
    agent_id: str
    project_id: str


class SummaryAgent:
    """
    Memory Summary Agent
    """

    def __init__(self, *, llm_client: BaseLLMClient, dependency: SummarizationDependency):
        """
        Initialize Summary Agent
        """
        self.llm_client = llm_client
        self.dependency = dependency

        settings = SettingsFactory.settings()
        self.max_reference_number = 100
        self.default_summary_length = settings.DEFAULT_SUMMARY_LENGTH

    async def summarize_memory(self, summary_categories: list[str]):
        """
        Summarize memory
        """

        async with SessionFactory.make_local_session() as db_session:
            memory_categories = await MemoryCategoryRepo.get_by_user_agent_group(
                user_id=self.dependency.user_id,
                agent_id=self.dependency.agent_id,
                project_id=self.dependency.project_id,
                db_session=db_session,
            )
            memory_categories = {category.name: category for category in memory_categories}

            for category_name in summary_categories:
                if category_name not in memory_categories:
                    logger.warning(
                        f"Skip category {category_name} not in memory_categories for {self.dependency.user_id}:{self.dependency.agent_id}"
                    )
                    continue

                logger.info(
                    f"Processing category {category_name} for {self.dependency.user_id}:{self.dependency.agent_id}"
                )

                memory_category = memory_categories[category_name]

                try:
                    if category_name == "profile":
                        summary_with_label = await self.summarize_profile(
                            summary_length=self.default_summary_length, db_session=db_session
                        )
                    elif memory_category.group == "basic":
                        project_memory_category = await ProjectMemCategoryRepo.get_by_project_and_category(
                            project_id=self.dependency.project_id,
                            name=category_name,
                            session=db_session,
                        )
                        summary_length = project_memory_category.config.get("summary_length", -1)
                        if summary_length <= 0:
                            summary_length = self.default_summary_length
                        summary_with_label = await self.summarize_category(
                            category_name=category_name, summary_length=summary_length, db_session=db_session
                        )
                    else:
                        summary_with_label = await self.summarize_category(
                            category_name=category_name,
                            summary_length=self.default_summary_length,
                            db_session=db_session,
                        )

                    label = summary_with_label["label"]
                    summary = summary_with_label["summary"]

                    summary_item = {
                        # "label": label,
                        "summary": summary,
                        # "timestamp": datetime.now(UTC).isoformat(),
                        "timestamp": pendulum.now("UTC").isoformat(),
                        "metadata": {
                            "method": "basic",
                            "target_length": summary_with_label["target_length"],
                            "actual_length": get_token_count(summary),
                        },
                    }

                    cateory_summary = memory_category.summary
                    if cateory_summary is None:
                        cateory_summary = {label: summary_item}
                    else:
                        # Create a copy to ensure SQLAlchemy detects the change
                        cateory_summary = cateory_summary.copy()
                        cateory_summary[label] = summary_item

                    await MemoryCategoryRepo.update(
                        current=memory_category,
                        new=IMemoryCategoryUpdate(summary=cateory_summary),
                        db_session=db_session,
                    )
                    await db_session.commit()

                except Exception as e:
                    logger.error(f"Error summarizing memory category {category_name}: {e!r}")
                    continue

    async def summarize_profile(self, summary_length: int, db_session: AsyncSession):
        """
        Summarize profile
        """

        memories = await MemoryRepo.get_by_category_top(
            category="profile",
            user_id=self.dependency.user_id,
            agent_id=self.dependency.agent_id,
            project_id=self.dependency.project_id,
            top_n=self.max_reference_number,
            order_by="updated_at",
            db_session=db_session,
        )
        memories = memories[::-1]

        memory_flattened = "\n".join([f"- {memory.content}" for memory in memories])

        summarize_prompt = f"""You are an expert in summarizing one's memory into a structured format.

Please read the following memory items and summarize them into a structured format by categories.

MEMORY ITEMS:
{memory_flattened}

Example of categories that you should consider:
1. Personal Information (mandatory)
   - Full name, age/birthdate, occupation, location, family members
2. Personality & Characteristics
   - Traits, habits, preferences, communication style
3. Relationships
   - Friends, colleagues, romantic interests, family dynamics
4. Interests & Activities
   - Hobbies, sports, entertainment preferences, regular activities
5. Life Situation
   - Current challenges, goals, living situation, health status
... other categories you find worth mentioning (if any)

Example Output:
## Personal Information
- Name: John Smith
- Age: 32 (born March 1991)
- Occupation: Software Engineer at TechCorp
- Location: Seattle, Washington
- Family: Sister (Sarah), parents live in Portland

## Personality & Characteristics
- Detail-oriented and analytical
- Prefers morning routines
- Values punctuality and organization
- Tends to overthink decisions

## Relationships
- Close friend: Mike (college roommate)
- Dating: Emily (met 3 months ago)
- Mentor: Dr. Richards (former professor)

## Interests & Activities
- Running (training for marathons)
- Reading science fiction novels
- Weekend hiking trips
- Learning Spanish on Duolingo

## Life Situation
- Recently promoted to senior engineer
- Saving for a house down payment
- Managing chronic back pain from old injury
- Planning to adopt a dog


Notes:
- Keep output concise but informative
- Each line should be an atomic piece of information, don't let a single line exceed 20 words.
- Avoid inference and deduction - only summarize information mentioned in the memory items. No need to perform deduction by yourself.
- Except for "Personal Information", all other categories are not mandatory. You can skip a category if no information related to that category is mentioned in the memory items.
- You can create new categories if you find worth-mentioning information related to a specific topic.
- **IMPORTANT**: Output only the content of the summary, no headers like "markdown:", no endings like "summary task complete".
- **IMPORTANT**: Control the total length of output to be not more than {summary_length} words. It's common that input memory items significantly exceed this length. You can select most important information based on your judgement to craft the summary.
"""

        # Call LLM to format content
        summary = self.llm_client.simple_chat(summarize_prompt)

        if summary.startswith("```markdown") and summary.endswith("```"):
            summary = summary[len("```markdown") : -len("```")]

        return {
            "label": f"basic-{summary_length}",
            "target_length": summary_length,
            "summary": summary,
        }

    async def summarize_category(self, category_name: str, summary_length: int, db_session: AsyncSession):
        """
        Summarize category
        """

        memories = await MemoryRepo.get_by_category_top(
            category=category_name,
            user_id=self.dependency.user_id,
            agent_id=self.dependency.agent_id,
            project_id=self.dependency.project_id,
            top_n=self.max_reference_number,
            order_by="updated_at",
            db_session=db_session,
        )
        memories = memories[::-1]

        memory_flattened = "\n".join([
            f"- [{memory.happened_at.strftime('%Y-%m-%d')}] {memory.content}" for memory in memories
        ])

        summarize_prompt = f"""You are an expert in summarizing one's memory into a structured format.

Please read the following memory items and summarize them in markdown format.

MEMORY ITEMS:
{memory_flattened}


Notes:
- Keep output concise but informative
- Avoid inference and deduction - only summarize information mentioned in the memory items. No need to perform deduction by yourself.
- Ensure that the date/time information of each memory you decide to put in the summary is also included in the summary.
- You can merge the date/time information reasonably and hierarchically if a series of memories happened at the same date/time, but ensure that a reader can understand when each memory happened from the summary.
- **IMPORTANT**: Output only the content of the summary, no headers like "markdown:", no endings like "summary task complete".
- **IMPORTANT**: Control the total length of output to be not more than {summary_length} words. It's common that input memory items significantly exceed this length. You can select most important information based on your judgement to craft the summary.
"""

        # Call LLM to format content
        summary = self.llm_client.simple_chat(summarize_prompt)

        if summary.startswith("```markdown") and summary.endswith("```"):
            summary = summary[len("```markdown") : -len("```")]

        return {
            "label": f"basic-{summary_length}",
            "target_length": summary_length,
            "summary": summary,
        }
