import copy
import time
from typing import Any

from pydantic import BaseModel

from app.tasks.memory.llm.aws_deepseek_client import AWSDeepSeekClient
from app.tasks.memory.llm.azure_openai_client import AzureOpenAIClient
from app.tasks.memory.llm.base import BaseEmbeddingClient, BaseLLMClient, EmbeddingResponse, LLMResponse
from app.tasks.memory.llm.deepseek_client import DeepSeekClient
from app.tasks.memory.llm.openai_client import OpenAIClient
from config.loader import SettingsFactory

from ..utils import get_logger

logger = get_logger(__name__)


class TokenUsage(BaseModel):
    """Token Usage Model"""

    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0


class ClientWrapper:
    def __init__(self, client: BaseLLMClient | BaseEmbeddingClient):
        self.client = client

        self.usage = TokenUsage()
        self.action_meta = {}
        self.error_record: list[dict[str, Any]] = []
        self.debug_record: list[dict[str, Any]] = []

        settings = SettingsFactory.settings()
        self.debug_info_level = settings.LLM_DEBUG_INFO_LEVEL.lower()

        self.raw_api_record = None
        # self._hook_api_raw()
        self.set_active_hook()

    def __getattr__(self, name: str):
        return getattr(self.client, name)

    def __del__(self):
        if hasattr(self, "unhook"):
            self.unhook()

    def _update_usage(self, usage: dict[str, Any]) -> None:
        try:
            self.usage.prompt_tokens += usage.get("prompt_tokens", 0)
            self.usage.completion_tokens += usage.get("completion_tokens", 0)
            self.usage.total_tokens += usage.get("total_tokens", 0)
        except Exception as e:
            logger.exception(f"Failed to update usage with {usage}: {e!r}")

    def clear_usage(self) -> None:
        """Clear the usage of the LLM client"""
        self.usage = TokenUsage()

    def generate_embedding(self, text: str, **kwargs) -> EmbeddingResponse:
        response = self.client.generate_embedding(text, **kwargs)
        self._update_usage(response.usage)
        return response

    def chat_completion(self, messages: list[dict[str, str]], **kwargs) -> LLMResponse:
        start_time = time.time()
        response = self.client.chat_completion(messages, **kwargs)
        end_time = time.time()
        self._log_auto(response, end_time - start_time)
        return response

    def simple_chat(self, prompt: str, **kwargs) -> str:
        messages = [{"role": "user", "content": prompt}]
        start_time = time.time()
        response = self.client.chat_completion(messages, **kwargs)
        end_time = time.time()
        self._log_auto(response, end_time - start_time)
        return response.content if response.success else f"Error: {response.error}"

    def set_action_meta(self, action_meta: dict[str, Any]) -> None:
        self.action_meta = action_meta

    def _add_action_meta(self, record: dict[str, Any]) -> None:
        return record | self.action_meta

    def _hook_api_raw(self) -> None:
        if isinstance(self.client, OpenAIClient) or isinstance(self.client, AzureOpenAIClient):
            original_create = self.client.chat.completions.create

            def hooked_create(*args, **kwargs):
                response = original_create(*args, **kwargs)
                self.raw_api_record = (kwargs, response)
                return response

            self.client.chat.completions.create = hooked_create

            def unhook():
                self.client.chat.completions.create = original_create

            self.unhook = unhook
        elif isinstance(self.client, DeepSeekClient):
            original_complete = self.client.client.complete

            def hooked_complete(*args, **kwargs):
                response = original_complete(*args, **kwargs)
                self.raw_api_record = (kwargs, response)
                return response

            self.client.client.complete = hooked_complete

            def unhook():
                self.client.client.complete = original_complete

            self.unhook = unhook
        elif isinstance(self.client, AWSDeepSeekClient):
            original_converse = self.client.client.converse

            def hooked_converse(*args, **kwargs):
                response = original_converse(*args, **kwargs)
                self.raw_api_record = (kwargs, response)
                return response

            self.client.client.converse = hooked_converse

            def unhook():
                self.client.client.converse = original_converse

            self.unhook = unhook

    def set_active_hook(self) -> None:
        def active_hook(record):
            self.raw_api_record = record

        self.client._api_debug_hook = active_hook

    def _log_auto(self, response: LLMResponse, time_cost: float) -> None:
        if not response.success:
            self.error_record.append({
                "error": response.error,
                "context": response.error_context,
            })
        self._update_usage(response.usage)
        logger.info(f"token usage this call: {response.usage!r}")
        if self.debug_info_level == "all" or (self.debug_info_level == "error" and not response.success):
            self.debug_record.append(
                self._add_action_meta({
                    "params": self.raw_api_record[0],
                    # BE CAREFUL: this may take too much storage space
                    # "response_raw": self.raw_api_record[1].model_dump(),
                    # "response_raw": safe_model_dump(self.raw_api_record[1]),
                    "response": patched_dump(response),
                    "token_usage": response.usage,
                    "time_use": time_cost,
                })
            )

    def get_simplified_record(self) -> dict[str, Any]:
        debug_record = copy.deepcopy(self.debug_record)
        last_agent_call = None
        for record in debug_record:
            if record["params"].get("tools") and not record.get("function_name"):
                messages = record["params"]["messages"]
                if last_agent_call is not None:
                    record["params"]["tools"] = "/* omitted */"
                    if len(messages) > len(last_agent_call["messages"]):
                        record["params"]["messages"] = ["/* omitted */", *messages[len(last_agent_call["messages"]) :]]
                last_agent_call = {"messages": messages}
        return debug_record


def debug_dump(obj: BaseModel) -> dict[str, Any]:
    import json

    out = {}
    for key, value in obj:
        try:
            if isinstance(value, BaseModel):
                out[key] = value.model_dump()
            else:
                out[key] = json.dumps(value)
        except Exception as e:
            out[key] = {"repr": repr(value), "error": repr(e)}
    return out


def patched_dump(obj: BaseModel) -> dict[str, Any]:
    try:
        return obj.model_dump()
    except Exception as e:
        if hasattr(obj, "tool_calls"):
            tool_calls_dump = [tool_call_item.model_dump() for tool_call_item in obj.tool_calls]
            obj = obj.model_copy()
            obj.tool_calls = tool_calls_dump
            try:
                return obj.model_dump()
            except Exception:
                pass
        return {"repr": repr(obj), "error": repr(e)}


def safe_model_dump(obj: Any) -> dict[str, Any]:
    """Safely serialize a Pydantic model, handling MockValSer errors"""
    try:
        # First try normal model_dump
        return obj.model_dump()
    except Exception as e:
        logger.warning(f"Failed to dump model with default settings: {e!r}")
        try:
            # Try excluding problematic fields that commonly cause issues
            return obj.model_dump(exclude={"choices", "function_call", "logprobs"})
        except Exception as e2:
            logger.warning(f"Failed to dump model with exclusions: {e2!r}")
            try:
                # Try with serialize_as_any for more flexible serialization
                return obj.model_dump(serialize_as_any=True)
            except Exception as e3:
                logger.error(f"All serialization attempts failed: {e3!r}")
                # Return basic info as fallback
                return {
                    "id": getattr(obj, "id", "unknown"),
                    "model": getattr(obj, "model", "unknown"),
                    "object": getattr(obj, "object", "unknown"),
                    "created": getattr(obj, "created", 0),
                    "serialization_error": str(e3),
                }
