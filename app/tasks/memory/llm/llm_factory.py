import os
from collections.abc import Callable

from ..utils import get_logger
from .aws_deepseek_client import AWSDeepSeekClient
from .azure_openai_client import AzureEmbeddingClient, AzureOpenAIClient
from .base import BaseEmbeddingClient, BaseLLMClient
from .client_wrapper import ClientWrapper
from .deepseek_client import DeepSeek<PERSON>lient
from .openai_client import OpenAIClient

logger = get_logger(__name__)


def get_wrapped_client():
    def wrapper(base_factory: Callable):
        def wrapped_func(*args, **kwargs):
            client = base_factory(*args, **kwargs)
            return ClientWrapper(client)

        return wrapped_func

    return wrapper


@get_wrapped_client()
def get_llm_client(
    task: str = "default",
    model: str | None = None,
    **kwargs,
) -> BaseLLMClient | BaseEmbeddingClient:
    if task == "embedding":
        # force use azure openai model
        return AzureEmbeddingClient(
            api_key=os.getenv("AZURE_API_KEY"),
            azure_endpoint=os.getenv("AZURE_ENDPOINT"),
            api_version=os.getenv("AZURE_API_VERSION"),
            model=os.getenv("EMBEDDING_MODEL"),
            **kwargs,
        )

    if not model:
        model = os.getenv("CLIENT_MODEL")

    if not model:
        raise ValueError("No valid model provided. Must be provided directly or in environment variable CLIENT_MODEL.")

    _provider = get_provider(model)

    # Wu: I copied most of code for models from MemU project,
    #     there are many duplications in parameter loading, consider fix this later
    if _provider == "openai":
        return OpenAIClient(
            api_key=os.getenv("OPENAI_API_KEY"),
            model=model,
            **kwargs,
        )
    elif _provider == "azure":
        return AzureOpenAIClient(
            api_key=os.getenv("AZURE_API_KEY"),
            azure_endpoint=os.getenv("AZURE_ENDPOINT"),
            api_version=os.getenv("AZURE_API_VERSION"),
            deployment_name=model,
            **kwargs,
        )
    elif _provider == "aws_deepseek":
        return AWSDeepSeekClient(
            model_id=os.getenv("AWS_MODEL_ID", model),
            **kwargs,
        )
    elif _provider == "deepseek":
        return DeepSeekClient(
            api_key=os.getenv("AZURE_API_KEY"),
            endpoint=os.getenv("DEEPSEEK_ENDPOINT"),
            api_version=os.getenv("DEEPSEEK_API_VERSION"),
            model_name=model,
            **kwargs,
        )


def get_provider(model: str) -> str:
    if "deepseek" in model.lower():
        if os.getenv("AWS_BEDROCK").lower() == "true":
            return "aws_deepseek"
        else:
            return "deepseek"
    elif os.getenv("AZURE").lower() == "true":
        return "azure"
    else:
        return "openai"
