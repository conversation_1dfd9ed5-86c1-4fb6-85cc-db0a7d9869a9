"""
Memory Repository

Centralized database operations for memory management
"""

import asyncio
from datetime import datetime
from typing import Any

from app.modules.memory.crud import MemoryCategoryRepo as memory_category_crud
from app.modules.memory.crud import MemoryHistoryRepo as memory_history_crud
from app.modules.memory.crud import MemoryRepo as memory_crud
from app.modules.memory.models import Memory, MemoryCategory
from app.modules.memory.schema import (
    IMemoryCategoryCreate,
    IMemoryCategoryUpdate,
    IMemoryCreate,
    IMemoryHistoryCreate,
    IMemoryUpdate,
)
from app.modules.organization.project.memory_category.crud import ProjectMemCategoryRepo as project_memory_category_crud
from app.modules.organization.project.memory_category.model import ProjectMemoryCategory
from app.services.db.session import SessionFactory
from app.tasks.memory.llm.base import BaseEmbeddingClient
from app.tasks.memory.llm.llm_factory import get_llm_client

from .agent.actions.base_action import ActionContext
from .utils import get_logger

logger = get_logger(__name__)


_replace_characters = {
    "‘": "'",
    "’": "'",
    "“": '"',
    "”": '"',
}


def _cleanup_text(text: str) -> str:
    for char, replacement in _replace_characters.items():
        text = text.replace(char, replacement)

    # only preserve ascii characters
    # text = "".join(char for char in text if ord(char) < 128)

    # still in testing, this may be unstable
    text = "".join(char for char in text if ord(char) != 0)

    return text


class MemoryRepository:
    """
    Centralized repository for all memory-related database operations

    Encapsulates database access with context parameters to reduce code duplication
    and provide a clean interface for memory operations.
    """

    def __init__(self, context: ActionContext, embedding_client: BaseEmbeddingClient):
        """
        Initialize repository with action context

        Args:
            context: ActionContext containing user_id, agent_id, conversation_id, etc.
            embedding_client: BaseEmbeddingClient for generating embeddings
        """

        self.user_id = context.user_id
        self.agent_id = context.agent_id
        self.conversation_id = context.conversation_id
        self.session_date = context.session_date
        self.api_key_id = context.api_key_id
        self.project_id = context.project_id

        self.embedding_client = embedding_client

        self.operation_record: dict[str, set[str]] = {
            "ADD": set(),
            "UPDATE": set(),
            "DELETE": set(),
            "TOUCH": set(),
        }
        self.memory_history_created: list[str] = []
        self.memory_category_touched: set[str] = set()

        self.init_jobs = {
            "prepare_basic_categories": self.prepare_basic_categories(),
        }

        self.post_jobs = {
            "update_memory_category_info": self.update_memory_category_info(),
        }

    async def do_init_jobs(self) -> dict[str, Any]:
        """Do initialization"""
        results = await asyncio.gather(*self.init_jobs.values())

        return {
            "success": all(result["success"] for result in results),
            "results": {name: result for name, result in zip(self.init_jobs.keys(), results, strict=False)},
            "errors": {
                name: result.get("error", "")
                for name, result in zip(self.init_jobs.keys(), results, strict=False)
                if not result["success"]
            },
        }

    async def do_post_jobs(self) -> dict[str, Any]:
        """Do post jobs"""
        results = await asyncio.gather(*self.post_jobs.values())

        return {
            "success": all(result["success"] for result in results),
            "results": {name: result for name, result in zip(self.post_jobs.keys(), results, strict=False)},
            "errors": {
                name: result.get("error", "")
                for name, result in zip(self.post_jobs.keys(), results, strict=False)
                if not result["success"]
            },
        }

    async def generate_embedding(self, text: str) -> list[float] | None:
        """Generate embedding for text using shared OpenAI client"""
        try:
            if not self.embedding_client:
                self.embedding_client = get_llm_client(task="embedding")

            response = self.embedding_client.generate_embedding(text)

            return response.embedding

        except Exception as e:
            logger.exception(f"Failed to generate embedding: {e}")
            return None

    async def prepare_basic_categories(self) -> dict[str, Any]:
        """Check if basic categories exist"""
        try:
            async with SessionFactory.make_local_session() as db_session:
                project_basic_categories = await project_memory_category_crud.get_active_categories(
                    project_id=self.project_id,
                    db_session=db_session,
                )
                if not project_basic_categories:
                    logger.warning(f"No basic categories found for project {self.project_id}")
                    return {"success": False, "error": "No basic categories found in project_memory_category table"}

            existing_categories = await self.get_memory_categories("basic")
            existing_categories = [category.name for category in existing_categories]

            categories_pulled = []

            for proj_category in project_basic_categories:
                if proj_category.name not in existing_categories:
                    category_create = IMemoryCategoryCreate(
                        user_id=self.user_id,
                        agent_id=self.agent_id,
                        project_id=self.project_id,
                        group="basic",
                        name=proj_category.name,
                        description=None,
                    )

                    async with SessionFactory.make_local_session() as db_session:
                        created_category = await memory_category_crud.create(new=category_create, db_session=db_session)

                        await db_session.commit()

                    logger.info(f"Pulled basic category {proj_category.name} from project_memory_category")

                    categories_pulled.append(created_category)

            return {"success": True, "categories_pulled": categories_pulled}

        except Exception as e:
            logger.error(f"Failed to prepare basic categories: {e!r}")
            return {"success": False, "error": repr(e)}

    async def update_memory_category_info(self) -> dict[str, Any]:
        """Update memory category info"""
        try:
            categories_updated = []
            for category_name in self.memory_category_touched:
                memory_items = await self.get_memory_items_in_category(category_name)

                category_update = IMemoryCategoryUpdate(
                    count=len(memory_items),
                )

                async with SessionFactory.make_local_session() as db_session:
                    category = await memory_category_crud.get_by_user_agent_name(
                        user_id=self.user_id,
                        agent_id=self.agent_id,
                        project_id=self.project_id,
                        name=category_name,
                        db_session=db_session,
                    )

                    if not category:
                        logger.warning(f"Category {category_name} not found in memory_categories")
                        continue

                    await memory_category_crud.update(
                        current=category,
                        new=category_update,
                        db_session=db_session,
                    )

                    await db_session.commit()

                    logger.info(f"Updated memory category info for {category_name}. Count: {len(memory_items)}")

                    categories_updated.append(category_name)

        except Exception as e:
            logger.error(f"Failed to update memory category info: {e!r}")
            return {"success": False, "error": repr(e)}
        else:
            return {"success": True, "categories_updated": categories_updated}

    async def get_project_memory_categories(self) -> list[ProjectMemoryCategory]:
        """Get memory category by project"""
        try:
            async with SessionFactory.make_local_session() as db_session:
                categories = await project_memory_category_crud.get_active_categories(
                    project_id=self.project_id,
                    db_session=db_session,
                )

            return categories

        except Exception as e:
            logger.error(f"Failed to get project memory categories: {e!r}")

    async def get_memory_categories(self, group: str) -> list[MemoryCategory]:
        """Get memory category by category class"""
        try:
            assert group in ["basic", "cluster"], "Invalid category class"

            async with SessionFactory.make_local_session() as db_session:
                categories = await memory_category_crud.get_by_user_agent_group(
                    user_id=self.user_id,
                    agent_id=self.agent_id,
                    project_id=self.project_id,
                    group=group,
                    db_session=db_session,
                )

            return categories

        except Exception as e:
            logger.error(f"Failed to get memory categories for {group}: {e!r}")
            return None

    async def get_memory_items_in_category(self, category: str) -> list[Memory]:
        """Get memory items by category"""
        try:
            async with SessionFactory.make_local_session() as db_session:
                items = await memory_crud.get_by_user_agent_category(
                    user_id=self.user_id,
                    agent_id=self.agent_id,
                    category=category,
                    project_id=self.project_id,
                    db_session=db_session,
                )

            return items

        except Exception as e:
            logger.error(f"Failed to get memory items for {category}: {e!r}")
            return None

    async def get_memory_item_by_id(self, category: str, memory_id: str) -> Memory:
        """Get memory item by id"""
        try:
            async with SessionFactory.make_local_session() as db_session:
                item = await memory_crud.get_by_user_agent_category_memory_id(
                    user_id=self.user_id,
                    agent_id=self.agent_id,
                    category=category,
                    memory_id=memory_id,
                    project_id=self.project_id,
                    db_session=db_session,
                )

            return item

        except Exception as e:
            logger.error(f"Failed to get memory item {memory_id} for {category}: {e!r}")
            return None

    async def store_memory_items(
        self,
        category: str,
        memory_items: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """
        Store memory items to database with embeddings

        Args:
            category: Memory category to store
            memory_items: List of memory items to store

        Returns:
            Dictionary containing lists of succeeded and failed memory IDs
        """

        category = _cleanup_text(category)
        result = {
            "succeeded": [],
            "failed": [],
        }

        for item in memory_items:
            try:
                assert "memory_id" in item, "memory_id is required"
                assert "content" in item, "content is required"

                memory_id = item["memory_id"]
                cleaned_content = _cleanup_text(item["content"])

                embedding = await self.generate_embedding(cleaned_content)

                links = item.get("links", [])

                memory_create = IMemoryCreate(
                    memory_id=memory_id,
                    agent_id=self.agent_id,
                    user_id=self.user_id,
                    conversation_id=self.conversation_id,
                    category=category,
                    content=cleaned_content,
                    embedding=embedding,
                    links=links,
                    happened_at=datetime.fromisoformat(self.session_date),
                    api_key_id=self.api_key_id,
                    project_id=self.project_id,
                )

                history_create = IMemoryHistoryCreate(
                    memory_id=memory_id,
                    agent_id=self.agent_id,
                    user_id=self.user_id,
                    conversation_id=self.conversation_id,
                    action="ADD",
                    category=category,
                    content_after=cleaned_content,
                    links_after=links,
                    timestamp=datetime.now(),
                )

                async with SessionFactory.make_local_session() as db_session:
                    created_memory = await memory_crud.create(new=memory_create, db_session=db_session)
                    created_history = await memory_history_crud.create(new=history_create, db_session=db_session)

                    await db_session.commit()

            except Exception as e:
                memory_id = item.get("memory_id", "")
                logger.error(f"Failed to store memory item {memory_id}: {e!r}")

                # Log detailed error information for debugging
                if hasattr(e, "details") and e.details:
                    logger.error(f"Error details for memory {memory_id}: {e.details}")

                # Log the memory item data that caused the error
                logger.error(f"Memory item data: {item}")
                logger.error(f"cleaned_content: {cleaned_content}")

                result["failed"].append(memory_id)
                continue
            else:
                # Wu: Or use memory_id?
                self.operation_record["ADD"].add(created_memory.id)
                self.memory_history_created.append(created_history.id)
                self.memory_category_touched.add(category)

                result["succeeded"].append(item["memory_id"])

        return result

    async def add_memory_item(
        self,
        category: str,
        memory_item: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Add a single memory item to database
        """

        try:
            assert "memory_id" in memory_item, "memory_id is required"
            assert "content" in memory_item, "content is required"

            category = _cleanup_text(category)

            memory_id = memory_item["memory_id"]
            cleaned_content = _cleanup_text(memory_item["content"])

            embedding = await self.generate_embedding(cleaned_content)

            links = memory_item.get("links", [])

            memory_create = IMemoryCreate(
                memory_id=memory_id,
                agent_id=self.agent_id,
                user_id=self.user_id,
                conversation_id=self.conversation_id,
                category=category,
                content=cleaned_content,
                embedding=embedding,
                links=links,
                happened_at=datetime.fromisoformat(self.session_date),
                api_key_id=self.api_key_id,
                project_id=self.project_id,
            )

            history_create = IMemoryHistoryCreate(
                memory_id=memory_id,
                agent_id=self.agent_id,
                user_id=self.user_id,
                conversation_id=self.conversation_id,
                action="ADD",
                category=category,
                content_after=cleaned_content,
                links_after=links,
                timestamp=datetime.now(),
            )

            async with SessionFactory.make_local_session() as db_session:
                created_memory = await memory_crud.create(new=memory_create, db_session=db_session)
                created_history = await memory_history_crud.create(new=history_create, db_session=db_session)

                await db_session.commit()

        except Exception as e:
            memory_id = memory_item.get("memory_id", "")
            logger.error(f"Failed to add memory item {memory_id}: {e!r}")

            # Log detailed error information for debugging
            if hasattr(e, "details") and e.details:
                logger.error(f"Error details for memory {memory_id}: {e.details}")

            # Log the memory item data that caused the error
            logger.error(f"Memory item data: {memory_item}")
            logger.error(f"cleaned_content: {cleaned_content}")

            return {
                "success": False,
                "error": repr(e),
            }
        else:
            self.operation_record["ADD"].add(created_memory.id)
            self.memory_history_created.append(created_history.id)
            self.memory_category_touched.add(category)

            return {
                "success": True,
                "memory_id": memory_id,
            }

    async def update_memory_item(
        self,
        category: str,
        memory_item: dict[str, Any],
        previous_memory: Memory | None = None,
    ) -> dict[str, Any]:
        """
        Update a single memory item in database
        """

        try:
            assert "memory_id" in memory_item, "memory_id is required"

            memory_id = memory_item["memory_id"]

            if previous_memory is None:
                previous_memory = await self.get_memory_item_by_id(category, memory_id)

                if not previous_memory:
                    logger.error(f"Previous memory {memory_id} not found in category {category}")
                    return {
                        "success": False,
                        "error": f"Previous memory {memory_id} not found in category {category}",
                    }

            update_params = {
                "memory_id": memory_id,
            }

            if "content" in memory_item:
                cleaned_content = _cleanup_text(memory_item["content"])
                update_params["content"] = cleaned_content
                update_params["embedding"] = await self.generate_embedding(cleaned_content)

            if "links" in memory_item:
                update_params["links"] = memory_item["links"]

            memory_update = IMemoryUpdate(**update_params)

            history_create = IMemoryHistoryCreate(
                memory_id=memory_id,
                agent_id=self.agent_id,
                user_id=self.user_id,
                conversation_id=self.conversation_id,
                action="UPDATE",
                category=category,
                content_before=previous_memory.content,
                content_after=cleaned_content if "content" in memory_item else previous_memory.content,
                links_before=previous_memory.links,
                links_after=memory_item["links"] if "links" in memory_item else previous_memory.links,
                timestamp=datetime.now(),
            )

            async with SessionFactory.make_local_session() as db_session:
                updated_memory = await memory_crud.update(
                    current=previous_memory, new=memory_update, db_session=db_session
                )
                created_history = await memory_history_crud.create(new=history_create, db_session=db_session)

                await db_session.commit()

        except Exception as e:
            logger.error(f"Failed to update memory item {memory_item.get('memory_id', '')}: {e!r}")
            return {
                "success": False,
                "error": repr(e),
            }
        else:
            self.operation_record["UPDATE"].add(updated_memory.id)
            self.memory_history_created.append(created_history.id)
            self.memory_category_touched.add(category)

            return {
                "success": True,
                "memory_id": memory_id,
            }

    async def delete_memory_item(
        self,
        category: str,
        memory_id: str,
        previous_memory: Memory | None = None,
    ) -> dict[str, Any]:
        """
        Delete a single memory item from database
        """

        try:
            # memory_id is already provided as parameter
            if previous_memory is None:
                previous_memory = await self.get_memory_item_by_id(category, memory_id)

                if not previous_memory:
                    logger.error(f"Previous memory {memory_id} not found in category {category}")
                    return {
                        "success": False,
                        "error": f"Previous memory {memory_id} not found in category {category}",
                    }

            history_create = IMemoryHistoryCreate(
                memory_id=memory_id,
                agent_id=self.agent_id,
                user_id=self.user_id,
                conversation_id=self.conversation_id,
                action="DELETE",
                category=category,
                content_before=previous_memory.content,
                content_after=None,
                links_before=previous_memory.links,
                links_after=None,
                timestamp=datetime.now(),
            )

            async with SessionFactory.make_local_session() as db_session:
                deleted_memory = await memory_crud.remove(
                    user_id=self.user_id,
                    agent_id=self.agent_id,
                    category=category,
                    memory_id=memory_id,
                    db_session=db_session,
                )
                created_history = await memory_history_crud.create(new=history_create, db_session=db_session)

                await db_session.commit()

        except Exception as e:
            logger.error(f"Failed to delete memory item {memory_id}: {e!r}")
            return {
                "success": False,
                "error": repr(e),
            }
        else:
            self.operation_record["DELETE"].add(deleted_memory.id)
            self.memory_history_created.append(created_history.id)
            self.memory_category_touched.add(category)

            return {
                "success": True,
                "memory_id": memory_id,
            }

    async def touch_memory_item(
        self,
        category: str,
        memory_id: str,
        previous_memory: Memory | None = None,
    ) -> dict[str, Any]:
        """
        Touch a single memory item in database
        """

        try:
            # memory_id is already provided as parameter

            if previous_memory is None:
                previous_memory = await self.get_memory_item_by_id(category, memory_id)

                if not previous_memory:
                    logger.error(f"Previous memory {memory_id} not found in category {category}")
                    return {
                        "success": False,
                        "error": f"Previous memory {memory_id} not found in category {category}",
                    }

            memory_update = IMemoryUpdate()

            history_create = IMemoryHistoryCreate(
                memory_id=memory_id,
                agent_id=self.agent_id,
                user_id=self.user_id,
                conversation_id=self.conversation_id,
                action="TOUCH",
                category=category,
                content_before=previous_memory.content,
                content_after=previous_memory.content,
                links_before=previous_memory.links,
                links_after=previous_memory.links,
                timestamp=datetime.now(),
            )

            async with SessionFactory.make_local_session() as db_session:
                touched_memory = await memory_crud.update(
                    current=previous_memory, new=memory_update, db_session=db_session
                )
                created_history = await memory_history_crud.create(new=history_create, db_session=db_session)

                await db_session.commit()

        except Exception as e:
            logger.error(f"Failed to touch memory item {memory_id}: {e!r}")
            return {
                "success": False,
                "error": repr(e),
            }
        else:
            self.operation_record["TOUCH"].add(touched_memory.id)
            self.memory_history_created.append(created_history.id)
            self.memory_category_touched.add(category)

            return {
                "success": True,
                "memory_id": memory_id,
            }

    async def new_memory_cluster(
        self,
        cluster_name: str,
        cluster_id: str | None = None,
        cluster_description: str = "",
    ) -> dict[str, Any]:
        """
        Create a new memory cluster
        """

        try:
            category_create = IMemoryCategoryCreate(
                user_id=self.user_id,
                agent_id=self.agent_id,
                project_id=self.project_id,
                group="cluster",
                category_id=cluster_id,
                name=cluster_name,
                description=cluster_description,
            )

            async with SessionFactory.make_local_session() as db_session:
                created_category = await memory_category_crud.create(new=category_create, db_session=db_session)

                await db_session.commit()

        except Exception as e:
            logger.error(f"Failed to create memory cluster {cluster_name}: {e!r}")
            return {
                "success": False,
                "error": repr(e),
            }

        else:
            return {
                "success": True,
                "category_id": created_category.id,
            }

    async def similarity_search(
        self,
        reference_memory: Memory | None = None,
        query_embedding: list[float] | None = None,
        query: str | None = None,
        **kwargs,
    ) -> list[Memory]:
        """
        Search for similar memories using embedding similarity
        """
        try:
            assert reference_memory is not None or query_embedding is not None or query is not None, (
                "reference_memory or query_embedding or query is required"
            )

            search_param = {}

            if reference_memory is not None:
                query_embedding = reference_memory.embedding

                search_param = {
                    "query_embedding": query_embedding,
                    "exclude_memory_id": reference_memory.memory_id,
                }
            elif query_embedding is not None:
                search_param = {
                    "query_embedding": query_embedding,
                }
            else:
                query_embedding = await self.generate_embedding(query)
                search_param = {
                    "query_embedding": query_embedding,
                }

            async with SessionFactory.make_local_session() as db_session:
                memories = await memory_crud.similarity_search(
                    **search_param,
                    user_id=self.user_id,
                    agent_id=self.agent_id,
                    project_id=self.project_id,
                    **kwargs,
                    db_session=db_session,
                )

                return memories

        except Exception as e:
            logger.error(f"Failed to search for similar memories: {e!r}")
            return []
