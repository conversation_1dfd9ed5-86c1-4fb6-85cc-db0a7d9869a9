from pydantic import TypeAdapter, ValidationError
from dataclasses import fields
from typing import Any, get_origin, get_args, Union
from app.services.config.spec import FieldSpec


def _unwrap_optional(typ: Any) -> Any:
    o = get_origin(typ)
    if o is Union:
        args = [a for a in get_args(typ) if a is not type(None)]
        if len(args) == 1:
            return args[0]
    return typ


def _field_type_for_path(root: Any, path: str) -> Any:
    cls = root
    parts = path.split(".")
    for p in parts[:-1]:
        f = next((f for f in fields(cls) if f.name == p), None)
        if f is None:
            raise KeyError(f"unknown path: {path}")
        cls = _unwrap_optional(f.type)
    f = next((f for f in fields(cls) if f.name == parts[-1]), None)
    if f is None:
        raise KeyError(f"unknown path: {path}")
    return f.type


def _is_compatible(value: Any, typ: Any) -> bool:
    type_adapter = TypeAdapter(typ)
    try:
        type_adapter.validate_python(value, strict=True)
        return True
    except ValidationError:
        return False


def try_convert(dataclass_obj: Any, path: str, spec: FieldSpec, value: Any) -> Any:
    target_type = _field_type_for_path(dataclass_obj, path)
    if not _is_compatible(value, target_type):
        if spec and spec.convert:
            try:
                new_value = spec.convert(value)
            except Exception as e:
                raise ValueError(f"convert error for {path}: {e}") from e
            else:
                return new_value
        else:
            raise ValueError(
                f"type mismatch for {path}: got {type(value).__name__}, need {_unwrap_optional(target_type)}; add convert=..."
            )
    else:
        return value
