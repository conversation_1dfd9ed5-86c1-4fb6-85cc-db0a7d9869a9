import os
from app.services.config.store import Store
from app.services.config.settings import AppSettings
from app.services.config.env_loader import EnvLoader


def dotenv_decider():
    env = os.environ.get("ENV", "dev").lower()
    if env == "prod":
        return None
    return ".env.dev"


def build_config_store():
    store = Store(AppSettings)
    store.set_dotenv_decider(dotenv_decider)
    store.register_loader(EnvLoader())
    return store


def view_settings(store: Store[AppSettings]) -> AppSettings:
    return store.view()
