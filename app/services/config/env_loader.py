from app.services.config.spec import StrOrFunc, Source
from typing import Any, Mapping, Hashable
import os


def Env(var: StrOrFunc | None = None, *, when=None, **meta) -> Source:
    return Source("env", locator=var, when=when, meta=meta)


class EnvLoader:
    kind = "env"

    def _infer(self, field_path: str, *, mode: str = "leaf") -> str:
        """
        mode="leaf": user.password -> PASSWORD 只取最后一段
        若想全路径：把 mode 改为 "path" -> USER_PASSWORD
        """
        import re

        if mode == "leaf":
            leaf = field_path.split(".")[-1]
            leaf = re.sub(r"([a-z0-9])([A-Z])", r"\1_\2", leaf)
            leaf = re.sub(r"[^A-Za-z0-9]+", "_", leaf)
            return leaf.upper()
        # path 模式
        parts = []
        for p in field_path.split("."):
            p = re.sub(r"([a-z0-9])([A-Z])", r"\1_\2", p)
            p = re.sub(r"[^A-Za-z0-9]+", "_", p)
            parts.append(p.upper())
        return "_".join(parts)

    def build_locator(self, *, field_path: str, source: Source, settings: Any) -> str:
        loc = source.locator(settings) if callable(source.locator) else source.locator
        return loc if loc else self._infer(field_path, mode=source.meta.get("mode", "path"))

    def batch_key(self, locator: str, source: Source) -> Hashable | None:
        return None

    async def fetch_one(self, locator: str, *, source: Source) -> str | None:
        return os.environ.get(locator)

    async def fetch_many(self, locators: list[str], *, source: Source) -> Mapping[str, str | None]:
        return {k: os.environ.get(k) for k in locators}
