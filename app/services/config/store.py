from dataclasses import is_dataclass
import threading
from typing import Any, Callable, Iterable, Optional, TypeVar, Generic

from app.services.config.spec import FieldSpec, collect_specs, build_dep_graph, assert_acyclic, Loader, Source
from app.services.config.typecast import try_convert
from app.services.reload.types import Change
from app.runtime import reloader as REGISTRY, logger

T = TypeVar("T")


class Store(Generic[T]):
    # --------- 只读视图 ----------
    class _RO:
        __slots__ = ("_s",)

        def __init__(self, s):
            object.__setattr__(self, "_s", s)

        def __getattr__(self, n):
            return getattr(self._s, n)

        def __dir__(self):
            return dir(self._s)

        def __setattr__(self, *_):
            raise AttributeError("settings is read-only; use Store APIs")

    # --------- 构造 ----------
    def __init__(self, cls: type[T], *, loaders: Iterable[Loader] = ()):
        if not is_dataclass(cls):
            raise TypeError("Store requires a dataclass Settings class")
        self._cls: type[T] = cls
        self._s: T = cls()
        self._specs: dict[str, FieldSpec] = collect_specs(cls)
        self._deps: dict[str, Iterable[str]] = build_dep_graph(self._specs)
        assert_acyclic(self._deps)
        self._lock = threading.RLock()
        self._version = 0

        self._loaders: dict[str, Loader] = {p.kind: p for p in loaders}

        self._dotenv_decider: Optional[Callable[[], str | Iterable[str] | None]] = None
        self._dotenv_tried = False

    # ======================
    # 公有工具 / 视图
    # ======================
    def view(self) -> T:
        return Store._RO(self._s)

    def register_loader(self, p: Loader) -> None:
        self._loaders[p.kind] = p

    def set_dotenv_decider(self, decider):
        self._dotenv_decider, self._dotenv_tried = decider, False

    def list_paths(self) -> list[str]:
        return list(self._specs.keys())

    # ======================
    # 对外主 API（返回 changes；默认自动提交到 REGISTRY）
    # ======================
    async def refresh_all(self, *, submit: bool = True) -> list[Change]:
        view_ro, changes = await self._collect_changes(self._refresh_all())
        if submit and changes:
            await self._submit(changes, view_ro)
        return changes

    async def refresh_group(self, group_path: str, *, submit: bool = True) -> list[Change]:
        view_ro, changes = await self._collect_changes(self._refresh_group(group_path))
        if submit and changes:
            logger.info(changes)
            await self._submit(changes, view_ro)
        return changes

    async def refresh_for(self, paths: Iterable[str], *, submit: bool = True) -> list[Change]:
        view_ro, changes = await self._collect_changes(self._refresh_for(paths))
        if submit and changes:
            await self._submit(changes, view_ro)
        return changes

    async def reload_field(self, path: str, *, submit: bool = True) -> list[Change]:
        view_ro, changes = await self._collect_changes(self._reload_field(path))
        if submit and changes:
            await self._submit(changes, view_ro)
        return changes

    # ======================
    # 内部：提交到 REGISTRY
    # ======================
    async def _submit(self, changes: list[Change], settings_view: T):
        try:
            await REGISTRY.submit(changes, settings_view)
        except Exception:
            # 这里换成你的日志/告警
            pass

    # ======================
    # 内部：快照 + 变更构造（old/new）
    # ======================
    async def _collect_changes(self, refresh_coro) -> tuple[T, list[Change]]:
        scope_paths = self.list_paths()
        before = self._snapshot(scope_paths) if scope_paths else {}

        changed_paths = await refresh_coro

        if before:
            changes = self._build_changes(before, changed_paths)
        else:
            # 本地快照：无 before，old=None，仅提供 new
            changes = [Change(p, None, self._get_path(p)) for p in changed_paths]
        return self.view(), changes

    def _snapshot(self, paths: Iterable[str]) -> dict[str, Any]:
        with self._lock:
            return {p: self._get_path(p) for p in paths}

    def _build_changes(self, before: dict[str, Any], changed_paths: Iterable[str]) -> list[Change]:
        out: list[Change] = []
        for p in changed_paths:
            old = before.get(p, None)
            new = self._get_path(p)
            if old != new:
                out.append(Change(p, old, new))
        return out

    # ======================
    # 内部：刷新流程（sources → 写回 → 传播）
    # ======================
    async def _refresh_all(self) -> list[str]:
        self._maybe_load_dotenv()
        first = await self._recalc_fields(self.list_paths())
        if first:
            more = await self._propagate(first)
            return first + more
        return first

    async def _refresh_group(self, group_path: str) -> list[str]:
        paths = self._paths_under(group_path)
        if not paths:
            return []
        first = await self._recalc_fields(paths)
        seeds = first or paths
        if not seeds:
            return first
        more = await self._propagate(seeds)
        return first + more

    async def _refresh_for(self, paths: Iterable[str]) -> list[str]:
        first = await self._recalc_fields(paths)
        more = await self._propagate(first or paths)
        return first + more

    async def _reload_field(self, path: str) -> Any:
        changed = await self._recalc_fields([path])
        if changed:
            await self._propagate(changed)
        return self._get_path(path)

    # ---- sources 分层批量解析 ----
    async def _resolve_from_sources(self, field_paths: list[str]) -> dict[str, Any]:
        specs = self._specs
        unresolved = {p for p in field_paths if getattr(specs.get(p), "sources", None)}
        resolved: dict[str, Any] = {}
        if not unresolved:
            return resolved

        max_len = max((len(specs[p].sources) for p in unresolved), default=0)
        for layer in range(max_len):
            batches: dict[tuple[str, Any], list[tuple[str, Source, str]]] = {}
            for p in list(unresolved):
                spec = specs[p]
                if layer >= len(spec.sources):
                    continue
                src: Source = spec.sources[layer]
                if src.when and not src.when(self._s):
                    continue
                loader = self._loaders.get(src.kind)
                if not loader:
                    continue
                locator = loader.build_locator(field_path=p, source=src, settings=self._s)
                key = (src.kind, loader.batch_key(locator, src))
                batches.setdefault(key, []).append((p, src, locator))

            if not batches:
                continue

            for (kind, _bk), items in batches.items():
                loader = self._loaders.get(kind)
                if not loader:
                    continue
                locs = list({loc for _, _, loc in items})
                got = await loader.fetch_many(locs, source=items[0][1])  # 批量取值
                for p, _src, loc in items:
                    if p in resolved:
                        continue
                    val = got.get(loc, None)
                    if val is not None:
                        resolved[p] = val
                        unresolved.discard(p)

            if not unresolved:
                break
        return resolved

    # ---- 写回 / 传播 ----
    async def _recalc_fields(self, field_paths: Iterable[str]) -> list[str]:
        targets = list(dict.fromkeys(field_paths))
        got = await self._resolve_from_sources(targets)

        changed: list[str] = []
        specs = self._specs
        with self._lock:
            for p in targets:
                spec = specs.get(p)
                parent, name = self._parent_and_name(p)
                oldv = getattr(parent, name)
                if p in got:
                    newv = try_convert(self._cls, p, spec, got[p])
                elif spec and getattr(spec, "computed", None):
                    newv = try_convert(self._cls, p, spec, spec.computed.fn(parent))
                else:
                    newv = oldv
                if newv != oldv:
                    setattr(parent, name, newv)
                    changed.append(p)
            if changed:
                self._version += 1
        return changed

    async def _propagate(self, seeds: Iterable[str]) -> list[str]:
        pending, visited = set(seeds), set()
        all_changed: list[str] = []
        while pending:
            src = pending.pop()
            if src in visited:
                continue
            visited.add(src)
            tgts = list(self._deps.get(src, ()))
            if not tgts:
                continue
            changed = await self._recalc_fields(tgts)
            if changed:
                all_changed.extend(changed)
                pending.update(changed)
        return all_changed

    # ---- 辅助：dotenv / 路径 ----
    def _maybe_load_dotenv(self) -> None:
        if self._dotenv_tried:
            return
        self._dotenv_tried = True
        if not self._dotenv_decider:
            return
        try:
            from pathlib import Path
            from dotenv import load_dotenv

            decision = self._dotenv_decider()
            if not decision:
                return
            paths = decision if isinstance(decision, (list, tuple, set)) else (decision,)
            for p in paths:
                path = Path(p)
                if path.exists():
                    load_dotenv(path, override=False)
        except Exception:
            pass

    def _parent_and_name(self, path: str):
        cur = self._s
        parts = path.split(".")
        for p in parts[:-1]:
            cur = getattr(cur, p)
        return cur, parts[-1]

    def _get_path(self, path: str) -> Any:
        cur = self._s
        for p in path.split("."):
            cur = getattr(cur, p)
        return cur

    def _paths_under(self, group_path: str) -> list[str]:
        prefix = group_path + "."
        return [p for p in self._specs.keys() if p.startswith(prefix) or p == group_path]
