from enum import StrEnum
from dataclasses import dataclass, field

from pydantic import PostgresDsn, TypeAdapter

from app.services.config.spec import Computed, SpecField
from app.services.config.env_loader import Env
# from app.services.aws.secrets.config_loader import Secret
# from app.system_parameter.config_loader import DB


class Environment(StrEnum):
    DEV = "dev"
    PROD = "prod"
    TEST = "test"


def to_type(typ):
    type_adapter = TypeAdapter(typ)
    return lambda v: type_adapter.validate_python(v)


# def is_prod(settings: "AppSettings") -> bool:
#     return settings.env == Environment.PROD


# @dataclass(slots=True)
# class Database:
#     host: str | None = SpecField("localhost",sources=(Env(),))
#     port: int = SpecField(5432, sources=(Env(),))
#     user: str | None = SpecField("postgres", sources=(Env(),))

#     @staticmethod
#     def password_path(s: "AppSettings") -> str:
#         return f"{s.service_name}/{str(s.env)}/db_memu_prod_admin_password"

#     password: str | None = SpecField(
#         "postgres",
#         sources=(Secret(password_path, selector="data", when=is_prod, deps=("service_name", "env", "aws_region")),
#                  Env())
#     )
#     name: str | None = SpecField("cloud_dev", sources=(Env(),))

#     def build_uri(self) -> PostgresDsn:
#         return PostgresDsn.build(
#             scheme="postgresql+psycopg",
#             username=self.user,
#             password=self.password,
#             host=self.host,
#             port=self.port,
#             path=self.name,
#         )

#     uri: PostgresDsn | str = SpecField(
#         "",
#         computed=Computed(
#             fn=build_uri,
#             inputs=("host", "port", "user","password", "name"),
#         )
#     )

#     enable_sql_logs: bool = SpecField(False, sources=(Env("ENABLE_SQL_LOGS"),), convert=to_type(bool))

#     db_pool_size: int = SpecField(default=80, sources=(Env("DATABASE_POOL_SIZE"),))
#     web_concurrency: int = SpecField(default=10, sources=(Env("DATABASE_WEB_CONCURRENCY"),))

#     def calc_pool_size(self):
#         return max(self.db_pool_size // self.web_concurrency, 5)
#     pool_size: int = SpecField(5, computed=Computed(fn=calc_pool_size, inputs=("db_pool_size", "web_concurrency")))

# @dataclass(slots=True)
# class LLM:
#     openai_endpoint: str | None = SpecField(
#         "PLACEHOLDER",
#         sources=(DB("llm", "openai_endpoint"), Env("OPENAI_ENDPOINT")))


@dataclass(slots=True)
class Stripe:
    sync_on_startup: bool = SpecField(False, sources=(Env(),), convert=to_type(bool))


@dataclass(slots=True)
class Logger:
    level: str = SpecField("INFO", sources=(Env("LOG_LEVEL"),))
    otlp_endpoint: str | None = SpecField("", sources=(Env(),))


@dataclass(slots=True)
class AppSettings:
    env: Environment = SpecField(Environment.DEV, sources=(Env(),), convert=to_type(Environment))
    project_name: str = SpecField("Memu api", sources=(Env(),))
    service_name: str = SpecField("memu-api", sources=(Env(),))
    aws_region: str = SpecField("us-east-1", sources=(Env(),))
    api_version: str = SpecField("v1", sources=(Env(),))

    CELERY_BROKER_URL: str = SpecField("redis://localhost:6379/0", sources=(Env(),))
    CELERY_RESULT_BACKEND: str = SpecField("redis://localhost:6379/1", sources=(Env(),))

    def build_api_version(self) -> str:
        return f"/api/{self.api_version}"

    api_v1_str: str = SpecField("/api/v1", computed=Computed(fn=build_api_version, inputs=("api_version",)))

    logger: Logger = field(default_factory=Logger)
    stripe: Stripe = field(default_factory=Stripe)
    # database: Database = field(default_factory=Database)
    # llm: LLM = field(default_factory=LLM)


STARTUP = ["env", "project_name", "service_name", "aws_region", "api_version", "logger.level"]
