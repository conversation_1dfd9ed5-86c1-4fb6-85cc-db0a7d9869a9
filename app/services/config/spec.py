import threading
from dataclasses import dataclass, field, fields, MISSING, is_dataclass
from typing import (
    Any,
    Callable,
    Generic,
    Iterable,
    Optional,
    TypeVar,
    get_origin,
    get_args,
    Union,
    Mapping,
    Protocol,
    Hashable,
)


# =====================================================
# 1) Source / Computed / FieldSpec + 辅助构造
# =====================================================

StrOrFunc = str | Callable[[Any], str]


@dataclass(frozen=True, slots=True)
class Source:
    kind: str
    locator: StrOrFunc
    selector: str | None = None
    when: Callable[[Any], bool] | None = None
    deps: tuple[str, ...] = ()
    meta: Mapping[str, Any] = field(default_factory=dict)


class Loader(Protocol):
    kind: str  # "env" | "ssm" | "secrets" | "db"

    def build_locator(self, *, field_path: str, source: Source, settings: Any) -> str: ...

    def batch_key(self, locator: str, source: Source) -> Hashable | None: ...

    async def fetch_one(self, locator: str, *, source: Source) -> Any | None: ...
    async def fetch_many(self, locators: list[str], *, source: Source) -> Mapping[str, Any | None]: ...


@dataclass(frozen=True, slots=True)
class Computed:
    fn: Callable[[Any], Any]
    inputs: tuple[str, ...]


@dataclass(frozen=True, slots=True)
class FieldSpec:
    sources: tuple[Source, ...] = ()
    computed: Optional[Computed] = None
    convert: Optional[Callable[[Any], Any]] = None


def SpecField(
    default=MISSING,
    *,
    sources: tuple[Source, ...] = (),
    computed: Optional[Computed] = None,
    convert: Optional[Callable[[Any], Any]] = None,
):
    meta = {"spec": FieldSpec(sources=sources, computed=computed, convert=convert)}
    if default is MISSING:
        return field(metadata=meta)
    return field(default=default, metadata=meta)


T = TypeVar("T")


@dataclass
class StoreState(Generic[T]):
    cls: type[T]
    s: T
    specs: dict[str, Any]
    deps: dict[str, Iterable[str]]
    loaders: dict[str, Loader]
    lock: threading.RLock
    version: int = 0
    dotenv_decider: Optional[Callable[[], str | Iterable[str] | None]] = None
    dotenv_tried: bool = False


# =====================================================
# 3) 规范收集 & 依赖图
# =====================================================


def collect_specs(root_cls: Any) -> dict[str, FieldSpec]:
    mapping: dict[str, FieldSpec] = {}

    def walk(prefix: str, cls: type):
        for f in fields(cls):
            path = f"{prefix}.{f.name}" if prefix else f.name
            spec: FieldSpec | None = f.metadata.get("spec")  # type: ignore
            if is_dataclass(f.type):
                walk(path, f.type if get_origin(f.type) is None else get_args(f.type)[0])
            else:
                if spec:
                    mapping[path] = spec

    walk("", root_cls)
    return mapping


def build_dep_graph(specs: dict[str, FieldSpec]) -> dict[str, list[str]]:
    """
    收集 computed.inputs 和 sources[*].deps
    构建有向图 upstream -> [downstreams...]
    """
    g: dict[str, list[str]] = {}

    def _resolve(dep: str, base: str) -> str:
        """
        路径解析规则：
        - 以 "/" 开头：从根开始的绝对路径（去掉前导 "/"）
        - 含 "."：当作绝对路径
        - 否则：相对当前 base
        """
        dep = dep.strip()
        if not dep:
            raise ValueError("Empty dependency path")
        if dep.startswith("/"):
            return dep[1:]
        return dep if "." in dep else (f"{base}.{dep}" if base else dep)

    def _add_edges(deps: Iterable[str], target: str, base: str) -> None:
        for d in deps:
            dep = _resolve(d, base)
            g.setdefault(dep, []).append(target)

    for path, spec in specs.items():
        base = ".".join(path.split(".")[:-1])  # 父前缀，如 "database.password" -> "database"

        # 1) computed 的依赖
        if getattr(spec, "computed", None) and getattr(spec.computed, "inputs", None):
            _add_edges(spec.computed.inputs, path, base)

        # 2) sources 的依赖
        for src in getattr(spec, "sources", None) or []:
            deps = getattr(src, "deps", ()) or ()
            if deps:
                _add_edges(deps, path, base)

    # 去重（保持原顺序）
    for k, v in g.items():
        seen, out = set(), []
        for x in v:
            if x not in seen:
                seen.add(x)
                out.append(x)
        g[k] = out

    return g


def assert_acyclic(graph: dict[str, list[str]], *, all_fields: Iterable[str] | None = None) -> None:
    color: dict[str, int] = {}
    stack: list[str] = []
    nodes: set[str] = set(graph.keys())
    for vs in graph.values():
        nodes.update(vs)
    if all_fields:
        nodes.update(all_fields)  # 可选

    def dfs(u: str):
        color[u] = 1
        stack.append(u)
        for v in graph.get(u, ()):
            c = color.get(v, 0)
            if c == 0:
                dfs(v)
            elif c == 1:
                i = next((i for i, n in enumerate(stack) if n == v), 0)
                cycle = stack[i:] + [v]
                raise ValueError("Dependency cycle detected: " + " -> ".join(cycle))
        stack.pop()
        color[u] = 2

    for n in nodes:
        if color.get(n, 0) == 0:
            dfs(n)
