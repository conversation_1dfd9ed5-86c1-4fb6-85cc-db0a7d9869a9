from dependency_injector import containers, providers

from app.services.config.client import build_config_store, view_settings
from app.services.logging.client import setup_logging_stack
from app.services.reload.client import build_reload_registry


class ServiceContainer(containers.DeclarativeContainer):
    config_store = providers.Singleton(build_config_store)
    settings = providers.Singleton(view_settings, store=config_store)

    logging_stack = providers.Resource(setup_logging_stack, settings=settings)
    reloader = providers.Resource(
        build_reload_registry,
        logging_stack=logging_stack,
    )
