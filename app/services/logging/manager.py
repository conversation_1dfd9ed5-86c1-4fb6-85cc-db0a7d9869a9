from app.services.config.settings import AppSettings
import threading
import logging


class LoggingManager:
    def __init__(
        self, logger: logging.Logger, settings: AppSettings, known=("app", "uvicorn", "uvicorn.error", "uvicorn.access")
    ):
        self._lock = threading.RLock()
        self._known = set(known)
        self.logger = logger
        self.apply(settings)

    def apply(self, settings: AppSettings):
        with self._lock:
            print("in logging manager", settings.logger.level.upper())
            self.logger.setLevel(settings.logger.level.upper())

            for name in self._known:
                if name not in self.logger.manager.loggerDict:
                    continue
                logger = logging.getLogger(name)
                logger.setLevel(settings.logger.level.upper())

            # 处理 uvicorn 的重复输出（让它们把日志冒泡给 root/OTel）
            for name in ("uvicorn", "uvicorn.error", "uvicorn.access"):
                if name in self._known:
                    logger = logging.getLogger(name)
                    logger.propagate = True
                    logger.handlers.clear()  # 避免和 OTel/控制台 handler 重复
