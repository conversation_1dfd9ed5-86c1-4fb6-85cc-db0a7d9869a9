from os import linesep
import pendulum
from opentelemetry.sdk._logs import LogRecord
from opentelemetry.trace import format_trace_id, format_span_id

RESET = "\x1b[0m"
BOLD = "\x1b[1m"
DIM = "\x1b[2m"
C = {
    "DEBUG": "\x1b[36m",  # cyan
    "INFO": "\x1b[32m",  # green
    "WARN": "\x1b[33m",  # yellow
    "WARNING": "\x1b[33m",
    "ERROR": "\x1b[31m",  # red
    "CRITICAL": "\x1b[31;1m",  # bright red
    "FATAL": "\x1b[31;1m",
}


def console_formatter():
    tz = pendulum.local_timezone()

    def fmt(r: LogRecord) -> str:
        ts = pendulum.from_timestamp((r.timestamp or 0) / 1e9, tz=tz).start_of("second")
        ts_str = ts.to_iso8601_string()
        sev = (r.severity_text or "-").upper()
        msg = str(r.body)
        trace = format_trace_id(r.trace_id) if r.trace_id else "-"
        span = format_span_id(r.span_id) if r.span_id else "-"

        sev_col = C.get(sev, "")
        ts_str = f"{DIM}{ts_str}{RESET}"
        sev_str = f"{BOLD}{sev_col}{sev}{RESET}"
        trace = f"{DIM}trace={trace}{RESET}"
        span = f"{DIM}span={span}{RESET}"

        return f"{ts_str} {sev_str}\t {trace} {span} {msg}{linesep}"

    return fmt
