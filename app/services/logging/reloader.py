from app.services.config.settings import AppSettings
from app.services.reload.types import Reloader
from app.services.logging.manager import LoggingManager
from app.services.logging.client import LoggingHandler


class LoggingReloader(Reloader):
    name = "logger"
    keys = {"logger."}
    immediate = True
    after = set()  # constraint of reload orders

    def __init__(self, stack: tuple[LoggingManager, LoggingHandler]):
        self.mgr = stack[0]

    async def prepare(self, settings: AppSettings) -> AppSettings:
        return settings

    async def validate(self, settings: AppSettings):
        pass

    async def swap(self, settings: AppSettings):
        self.mgr.apply(settings)

    async def cleanup(self, _): ...
