from typing import Generator
from app.services.config.settings import AppSettings, Environment
from contextlib import contextmanager
from opentelemetry import _logs
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor, ConsoleLogExporter, SimpleLogRecordProcessor
from opentelemetry.exporter.otlp.proto.http._log_exporter import OTLPLogExporter
from app.services.logging.formatter import console_formatter
import logging
from app.services.logging.manager import LoggingManager


@contextmanager
def start_logging_manager(*, settings: AppSettings):
    root = logging.getLogger()
    prev_root_level = root.level

    mgr = LoggingManager(root, settings)
    try:
        yield mgr
    finally:
        root.setLevel(prev_root_level)


@contextmanager
def setup_otel_logs(*, settings: AppSettings):
    provider = LoggerProvider(resource=Resource.create({"service.name": settings.service_name}))
    _logs.set_logger_provider(provider)

    if settings.env == Environment.PROD and settings.otlp_endpoint:
        exporter = OTLPLogExporter(endpoint=settings.otlp_endpoint)
        provider.add_log_record_processor(BatchLogRecordProcessor(exporter))

    exporter = ConsoleLogExporter(formatter=console_formatter())
    provider.add_log_record_processor(SimpleLogRecordProcessor(exporter))
    handler = LoggingHandler(level=logging.NOTSET, logger_provider=provider)
    root = logging.getLogger()
    root.addHandler(handler)
    try:
        yield handler
    finally:
        root.removeHandler(handler)
        try:
            provider.shutdown()
        except Exception:
            pass


@contextmanager
def setup_logging_stack(*, settings: AppSettings) -> Generator[tuple[LoggingManager, LoggingHandler]]:
    with start_logging_manager(settings=settings) as mgr:
        with setup_otel_logs(settings=settings) as handler:
            yield (mgr, handler)
