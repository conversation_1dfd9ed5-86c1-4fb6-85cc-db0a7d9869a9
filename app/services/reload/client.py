from contextlib import asynccontextmanager
from typing import AsyncGenerator
from .registry import ReloadRegistry
from app.services.logging.reloader import Logging<PERSON><PERSON><PERSON>


@asynccontextmanager
async def build_reload_registry(*, logging_stack) -> AsyncGenerator[ReloadRegistry, None]:
    reg = ReloadRegistry()
    reg.register(LoggingReloader(logging_stack))
    try:
        yield reg
    finally:
        await reg.close()
