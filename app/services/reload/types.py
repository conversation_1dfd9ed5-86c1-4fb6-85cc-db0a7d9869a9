from dataclasses import dataclass
from typing import Any, Iterable, Protocol


@dataclass(frozen=True)
class Change:
    key: str
    old: Any
    new: Any


class Reloader(Protocol):
    """四阶段：prepare -> validate -> swap -> cleanup（可按需实现为空）"""

    name: str
    keys: set[str]  # 例如 {"llm.", "logging.level"}
    after: set[str]  # 依赖其他 reloader 的名字

    async def prepare(self, settings) -> Any: ...
    async def validate(self, new_instance: Any) -> None: ...
    async def swap(self, new_instance: Any) -> Any | None: ...
    async def cleanup(self, old_instance: Any | None) -> None: ...


def touched(changes: Iterable[Change], prefixes: Iterable[str]) -> bool:
    ps = tuple(prefixes)
    return any(ch.key.startswith(ps) for ch in changes)
