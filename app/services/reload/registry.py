import asyncio
from collections import defaultdict, deque
from typing import Dict, List, Iterable, Set, Optional
from .types import Reloader, Change, touched


class ReloadRegistry:
    def __init__(self, debounce_ms: int = 300):
        self._items: Dict[str, Reloader] = {}
        self._lock = asyncio.Lock()
        self._debounce = debounce_ms / 1000.0
        self._pending: Dict[str, Change] = {}  # key -> 合并后的 Change
        self._last_settings = None
        self._timer: Optional[asyncio.Task] = None
        self._closed = False

    def register(self, r: Reloader):
        if r.name in self._items:
            raise ValueError(f"reloader duplicate: {r.name}")
        # 允许 Reloader 没定义 immediate，默认为 False
        if not hasattr(r, "immediate"):
            r.immediate = False  # type: ignore[attr-defined]
        self._items[r.name] = r

    # ---- impacted & 分组 ----
    def _impacted(self, changes: List[Change]) -> Dict[str, Reloader]:
        return {n: r for n, r in self._items.items() if touched(changes, r.keys)}

    def _immediacy_closure(self, impacted: Dict[str, Reloader]) -> Set[str]:
        """把 immediate 组按 after 依赖闭包（若 immediate 依赖到 impacted 内的非 immediate，则一并提升）"""
        imm: Set[str] = {n for n, r in impacted.items() if getattr(r, "immediate", False)}
        changed = True
        while changed:
            changed = False
            for n in list(imm):
                for dep in impacted[n].after:
                    if dep in impacted and dep not in imm:
                        imm.add(dep)
                        changed = True
        return imm

    def _filter_changes_for(self, changes: List[Change], names: Set[str]) -> List[Change]:
        """仅保留会命中给定 reloader 名集的 changes"""
        if not names:
            return []
        # 把这些 reloader 的所有前缀合并
        prefixes: List[str] = []
        for n in names:
            prefixes.extend(self._items[n].keys)
        return [c for c in changes if any(c.key.startswith(p) for p in prefixes)]

    # ---- 公开：立即运行（可限制 only）----
    async def run(self, changes: List[Change], settings, only: Optional[Set[str]] = None):
        impacted = self._impacted(changes)
        if only is not None:
            impacted = {n: r for n, r in impacted.items() if n in only}
        if not impacted:
            return
        order = self._toposort(impacted)
        async with self._lock:
            for r in order:
                new_obj = await r.prepare(settings)
                await r.validate(new_obj)
                old_obj = await r.swap(new_obj)
                await r.cleanup(old_obj)

    # ---- 公开：提交（immediate 立刻 run，其余进入防抖）----
    async def submit(self, changes: List[Change], settings):
        if self._closed or not changes:
            return

        impacted = self._impacted(changes)
        if not impacted:
            return

        imm_names = self._immediacy_closure(impacted)
        delayed_names = set(impacted) - imm_names

        # 1) 立即执行部分
        if imm_names:
            imm_changes = self._filter_changes_for(changes, imm_names)
            if imm_changes:
                # 立即执行只限这些 reloader
                await self.run(imm_changes, settings, only=imm_names)

        # 2) 防抖队列部分
        if delayed_names:
            # 只把命中 delayed 组的 changes 入队，避免重复执行 immediate
            delayed_changes = self._filter_changes_for(changes, delayed_names)
            if delayed_changes:
                if self._debounce <= 0:
                    await self.run(delayed_changes, settings, only=delayed_names)
                    return
                async with self._lock:
                    for ch in delayed_changes:
                        prev = self._pending.get(ch.key)
                        self._pending[ch.key] = Change(ch.key, prev.old if prev else ch.old, ch.new)
                    self._last_settings = settings
                    if self._timer is None:
                        self._timer = asyncio.create_task(self._flush_after_delay())

    async def _flush_after_delay(self):
        try:
            await asyncio.sleep(self._debounce)
            async with self._lock:
                batch = list(self._pending.values())
                settings = self._last_settings
                self._pending.clear()
                self._timer = None
            if batch:
                # flush 时不区分 immediate/非 immediate（此处队列只存了 delayed 的）
                await self.run(batch, settings)
        except asyncio.CancelledError:
            async with self._lock:
                batch = list(self._pending.values())
                settings = self._last_settings
                self._pending.clear()
                self._timer = None
            if batch:
                await self.run(batch, settings)
            raise

    async def flush_pending(self):
        async with self._lock:
            t = self._timer
            if not self._pending and not (t and not t.done()):
                return
            if t and not t.done():
                t.cancel()
        if t:
            try:
                await t
            except asyncio.CancelledError:
                pass

    async def close(self):
        self._closed = True
        await self.flush_pending()

    def _toposort(self, impacted: Dict[str, Reloader]) -> List[Reloader]:
        indeg = defaultdict(int)
        graph = defaultdict(list)
        for n, r in impacted.items():
            for dep in r.after:
                if dep in impacted:
                    graph[dep].append(n)
                    indeg[n] += 1
            indeg.setdefault(n, 0)
        q = deque([n for n, d in indeg.items() if d == 0])
        order = []
        while q:
            n = q.popleft()
            order.append(impacted[n])
            for m in graph[n]:
                indeg[m] -= 1
                if indeg[m] == 0:
                    q.append(m)
        if len(order) != len(impacted):
            return sorted(impacted.values(), key=lambda r: r.name)
        return order
