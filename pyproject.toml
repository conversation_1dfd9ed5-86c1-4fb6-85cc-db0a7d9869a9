[project]
name = "hippocampus-cloud"
version = "0.0.1"
description = "memory service for Persona AI"
authors = [{ name = "<PERSON> <PERSON>", email = "an<PERSON><PERSON>@gmail.com" }]
readme = "README.md"
keywords = ['python']
requires-python = ">=3.12,<4.0"
classifiers = [
    "Intended Audience :: Developers",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "alembic>=1.16.2",
    "azure-ai-inference>=1.0.0b9",
    "boto3>=1.40.5",
    "celery>=5.5.3",
    "dependency-injector>=4.48.1",
    "fastapi-pagination[sqlalchemy]>=0.13.3",
    "fastapi[standard]>=0.115.14",
    "firebase-admin>=6.9.0",
    "gunicorn>=23.0.0",
    "loguru>=0.7.3",
    "msgpack>=1.1.1",
    "openai>=1.54.4",
    "opentelemetry-distro>=0.57b0",
    "opentelemetry-exporter-otlp>=1.36.0",
    "passlib>=1.7.4",
    "pendulum>=3.1.0",
    "pgvector>=0.3.2",
    "psycopg[binary,pool]>=3.2.9",
    "pydantic-settings>=2.10.1",
    "pyjwt>=2.10.1",
    "redis>=6.2.0",
    "sqlalchemy[asyncio]>=2.0.41",
    "sqlmodel>=0.0.24",
    "stripe>=12.3.0",
    "svix-ksuid>=0.6.2",
    "tiktoken>=0.3.3",
]

[project.urls]
Homepage = "https://ankaisen.github.io/hippocampus-cloud/"
Repository = "https://github.com/ankaisen/hippocampus-cloud"
Documentation = "https://ankaisen.github.io/hippocampus-cloud/"

[dependency-groups]
dev = [
    "pytest>=7.2.0",
    "pre-commit>=2.20.0",
    "tox-uv>=1.11.3",
    "deptry>=0.23.0",
    "mypy>=0.991",
    "pytest-cov>=4.0.0",
    "ruff>=0.11.5",
    "mkdocs>=1.4.2",
    "mkdocs-material>=8.5.10",
    "mkdocstrings[python]>=0.26.1",
    "pytest-asyncio>=1.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app", "config"]

[tool.mypy]
files = ["app", "config"]
disallow_untyped_defs = true
disallow_any_unimported = true
no_implicit_optional = true
check_untyped_defs = true
warn_return_any = true
warn_unused_ignores = true
show_error_codes = true

[tool.pytest.ini_options]
testpaths = ["tests"]
log_cli = true
log_cli_level = "INFO"

[tool.ruff]
target-version = "py312"
line-length = 120
fix = true

[tool.ruff.lint]
select = [
    # flake8-2020
    "YTT",
    # flake8-bandit
    "S",
    # flake8-bugbear
    "B",
    # flake8-builtins
    "A",
    # flake8-comprehensions
    "C4",
    # flake8-debugger
    "T10",
    # flake8-simplify
    "SIM",
    # isort
    "I",
    # mccabe
    "C90",
    # pycodestyle
    "E", "W",
    # pyflakes
    "F",
    # pygrep-hooks
    "PGH",
    # pyupgrade
    "UP",
    # ruff
    "RUF",
    # tryceratops
    "TRY",
]
ignore = [
    # LineTooLong
    "E501",
    # DoNotAssignLambda
    "E731",
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101"]

[tool.ruff.format]
preview = true

[tool.coverage.report]
skip_empty = true

[tool.coverage.run]
branch = true
source = ["app"]
