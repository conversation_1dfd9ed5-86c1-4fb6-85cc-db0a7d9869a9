"""create stripe webhook events table

Revision ID: 3hp8usy983du
Revises: a7b3c9d2e5f8
Create Date: 2025-08-22 00:13:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '3hp8usy983du'
down_revision: Union[str, Sequence[str], None] = 'a7b3c9d2e5f8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "stripe_webhook_events",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("stripe_event_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column("event_type", sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
        sa.Column("stripe_created", sa.DateTime(timezone=True), nullable=False),
        sa.Column("livemode", sa.Boolean(), nullable=False),
        sa.Column("status", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("processed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("attempts", sa.Integer(), nullable=False),
        sa.Column("last_attempt_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("event_data", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("processing_result", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("identity_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
        sa.Column("organization_id", sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
        sa.ForeignKeyConstraint(
            ["identity_id"],
            ["identities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_stripe_webhook_events_id"), "stripe_webhook_events", ["id"], unique=False)
    op.create_index(op.f("ix_stripe_webhook_events_stripe_event_id"), "stripe_webhook_events", ["stripe_event_id"], unique=True)
    op.create_index(op.f("ix_stripe_webhook_events_event_type"), "stripe_webhook_events", ["event_type"], unique=False)
    op.create_index(op.f("ix_stripe_webhook_events_status"), "stripe_webhook_events", ["status"], unique=False)
    op.create_index(op.f("ix_stripe_webhook_events_identity_id"), "stripe_webhook_events", ["identity_id"], unique=False)
    op.create_index(op.f("ix_stripe_webhook_events_organization_id"), "stripe_webhook_events", ["organization_id"], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f("ix_stripe_webhook_events_organization_id"), table_name="stripe_webhook_events")
    op.drop_index(op.f("ix_stripe_webhook_events_identity_id"), table_name="stripe_webhook_events")
    op.drop_index(op.f("ix_stripe_webhook_events_status"), table_name="stripe_webhook_events")
    op.drop_index(op.f("ix_stripe_webhook_events_event_type"), table_name="stripe_webhook_events")
    op.drop_index(op.f("ix_stripe_webhook_events_stripe_event_id"), table_name="stripe_webhook_events")
    op.drop_index(op.f("ix_stripe_webhook_events_id"), table_name="stripe_webhook_events")
    op.drop_table("stripe_webhook_events")
