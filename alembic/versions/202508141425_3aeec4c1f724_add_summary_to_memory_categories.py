"""add summary to memory categories

Revision ID: 3aeec4c1f724
Revises: e0942ceb5cbb
Create Date: 2025-08-14 14:25:33.842632

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3aeec4c1f724"
down_revision: str | Sequence[str] | None = "e0942ceb5cbb"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column("memory_categories", sa.Column("summary", postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.drop_constraint(
        op.f("project_memory_categories_project_id_name_type_key"), "project_memory_categories", type_="unique"
    )
    op.create_unique_constraint(None, "project_memory_categories", ["project_id", "name"])


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_constraint(None, "project_memory_categories", type_="unique")
    op.create_unique_constraint(
        op.f("project_memory_categories_project_id_name_type_key"),
        "project_memory_categories",
        ["project_id", "name", "type"],
        postgresql_nulls_not_distinct=False,
    )
    op.drop_column("memory_categories", "summary")
